{/* Enhanced 2-Column Form Layout with Collapsible Categories */}
<div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-8">

  {/* Column 1: Collapsible Categories */}
  <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
    <div className="text-center mb-8">
      <h3 className="text-2xl font-bold text-white mb-3">Project Information</h3>
      <p className="text-slate-300 text-base">Complete all project details and settings</p>
    </div>

    {/* Collapsible Categories */}
    <div className="space-y-6">
      
      {/* Client Information Category */}
      <div className="bg-white/5 border border-white/10 rounded-2xl overflow-hidden">
        <button
          onClick={() => toggleCategory('clientInfo')}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h4 className="text-lg font-bold text-white">Client Information</h4>
              <p className="text-sm text-slate-300">Contact details and company info</p>
            </div>
          </div>
          <svg 
            className={`w-5 h-5 text-white transition-transform duration-300 ${collapsedCategories.clientInfo ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {!collapsedCategories.clientInfo && (
          <div className="px-6 pb-6 space-y-4">
            {/* Client Name */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Client Name *</label>
              <input
                type="text"
                value={formData.clientName}
                onChange={(e) => setFormData(prev => ({ ...prev, clientName: e.target.value }))}
                placeholder="Enter client name"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Client Company */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Company *</label>
              <input
                type="text"
                value={formData.clientCompany}
                onChange={(e) => setFormData(prev => ({ ...prev, clientCompany: e.target.value }))}
                placeholder="Enter company name"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Client Email */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Email</label>
              <input
                type="email"
                value={formData.clientEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, clientEmail: e.target.value }))}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Client Phone */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Phone</label>
              <input
                type="tel"
                value={formData.clientPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, clientPhone: e.target.value }))}
                placeholder="+****************"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Project Name */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Project Name *</label>
              <input
                type="text"
                value={formData.projectName}
                onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                placeholder="Enter project name"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Project Type */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Project Type</label>
              <select
                value={formData.projectType}
                onChange={(e) => setFormData(prev => ({ ...prev, projectType: e.target.value }))}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
              >
                <option value="">Select project type</option>
                {autoSuggestions.projectTypes.map((type, index) => (
                  <option key={index} value={type} className="bg-slate-800">{type}</option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* User Information Category */}
      <div className="bg-white/5 border border-white/10 rounded-2xl overflow-hidden">
        <button
          onClick={() => toggleCategory('userInfo')}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h4 className="text-lg font-bold text-white">Your Information</h4>
              <p className="text-sm text-slate-300">Company details and contact info</p>
            </div>
          </div>
          <svg 
            className={`w-5 h-5 text-white transition-transform duration-300 ${collapsedCategories.userInfo ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {!collapsedCategories.userInfo && (
          <div className="px-6 pb-6 space-y-4">
            {/* User Company Name */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Your Company</label>
              <input
                type="text"
                value={formData.userCompanyName}
                onChange={(e) => setFormData(prev => ({ ...prev, userCompanyName: e.target.value }))}
                placeholder="Your company name"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* User Contact Name */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Your Name</label>
              <input
                type="text"
                value={formData.userContactName}
                onChange={(e) => setFormData(prev => ({ ...prev, userContactName: e.target.value }))}
                placeholder="Your full name"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* User Email */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Your Email</label>
              <input
                type="email"
                value={formData.userEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, userEmail: e.target.value }))}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* User Phone */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Your Phone</label>
              <input
                type="tel"
                value={formData.userPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, userPhone: e.target.value }))}
                placeholder="+****************"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* User Website */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Website</label>
              <input
                type="url"
                value={formData.userWebsite}
                onChange={(e) => setFormData(prev => ({ ...prev, userWebsite: e.target.value }))}
                placeholder="https://yourcompany.com"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>
          </div>
        )}
      </div>

      {/* Timeline Category */}
      <div className="bg-white/5 border border-white/10 rounded-2xl overflow-hidden">
        <button
          onClick={() => toggleCategory('timeline')}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h4 className="text-lg font-bold text-white">Timeline</h4>
              <p className="text-sm text-slate-300">Project dates and duration</p>
            </div>
          </div>
          <svg 
            className={`w-5 h-5 text-white transition-transform duration-300 ${collapsedCategories.timeline ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {!collapsedCategories.timeline && (
          <div className="px-6 pb-6 space-y-4">
            {/* Start Date */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Start Date</label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* End Date */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">End Date</label>
              <div className="space-y-2">
                <button
                  onClick={() => setFormData(prev => ({ ...prev, endDate: 'TBD' }))}
                  className={`w-full px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                    formData.endDate === 'TBD'
                      ? 'bg-orange-500/20 border border-orange-400/50 text-orange-300'
                      : 'bg-white/10 border border-white/20 text-slate-300 hover:bg-white/20'
                  }`}
                >
                  TBD
                </button>
                <input
                  type="date"
                  value={formData.endDate === 'TBD' ? '' : formData.endDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                />
              </div>
            </div>

            {/* Duration - Auto-calculated */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">
                Duration
                <span className="ml-2 text-xs text-blue-300">(Auto-calculated)</span>
              </label>
              <div className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white transition-all duration-300 flex items-center justify-between">
                <span className={`text-sm ${formData.duration === 'TBD' || formData.duration === 'Invalid dates' ? 'text-slate-400' : 'text-green-300'}`}>
                  {formData.duration || 'Select start and end dates'}
                </span>
                <div className="flex items-center space-x-1 text-xs text-slate-400">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  <span>Auto</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Budget Category */}
      <div className="bg-white/5 border border-white/10 rounded-2xl overflow-hidden">
        <button
          onClick={() => toggleCategory('budget')}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div>
              <h4 className="text-lg font-bold text-white">Budget</h4>
              <p className="text-sm text-slate-300">Pricing and payment schedule</p>
            </div>
          </div>
          <svg 
            className={`w-5 h-5 text-white transition-transform duration-300 ${collapsedCategories.budget ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        {!collapsedCategories.budget && (
          <div className="px-6 pb-6 space-y-4">
            {/* Budget Section */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-white font-semibold mb-2 text-sm">Hourly Rate</label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.hourlyRate}
                  onChange={(e) => {
                    const newRate = e.target.value;
                    setFormData(prev => ({ ...prev, hourlyRate: newRate }));
                    calculateBudget(formData.estimatedHours, newRate);
                  }}
                  placeholder="150"
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                />
              </div>
              <div>
                <label className="block text-white font-semibold mb-2 text-sm">Est. Hours</label>
                <input
                  type="number"
                  min="0"
                  step="0.5"
                  value={formData.estimatedHours}
                  onChange={(e) => {
                    const newHours = e.target.value;
                    setFormData(prev => ({ ...prev, estimatedHours: newHours }));
                    calculateBudget(newHours, formData.hourlyRate);
                  }}
                  placeholder="80"
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                />
              </div>
            </div>

            {/* Total Budget */}
            <div>
              <label className="block text-white font-semibold mb-2 text-sm">Total Budget</label>
              <div className="relative">
                <div className="w-full px-4 py-3 bg-green-500/10 border border-green-400/20 rounded-lg text-green-300 font-bold text-lg text-center">
                  ${formatBudget(formData.totalBudget)}
                </div>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <p className="text-xs text-slate-400 mt-1">Auto-calculated from rate × hours</p>
            </div>
          </div>
        )}
      </div>

    </div>
  </div>
