module.exports = {
  reactStrictMode: true,
  output: 'standalone', // Enable standalone output for Docker deployment
  images: {
    domains: ['your-image-domain.com'], // Add your image domains here
    unoptimized: true // Disable image optimization for Railway
  },
  env: {
    API_URL: process.env.API_URL, // Example of using environment variables
  },
  // Disable caching for Railway deployment to avoid stale cache issues
  experimental: {
    // isrMemoryCacheSize is deprecated in Next.js 15+
  },
  webpack: (config, { dev, isServer }) => {
    // Disable caching in production builds to avoid Railway deployment issues
    if (!dev && !isServer) {
      config.cache = false;
    }

    // Custom webpack configurations can be added here
    return config;
  },
};