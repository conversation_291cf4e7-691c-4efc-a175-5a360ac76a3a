import { NextRequest, NextResponse } from 'next/server';
import { generateSOWContent } from '@/lib/ai/content-generator';

export async function POST(request: NextRequest) {
  try {
    const { projectDetails, companyInfo } = await request.json();
    const generatedContent = await generateSOWContent(projectDetails, companyInfo);
    
    return NextResponse.json({ content: generatedContent }, { status: 200 });
  } catch (error) {
    console.error('Error generating SOW content:', error);
    return NextResponse.json(
      { error: 'Failed to generate SOW content' }, 
      { status: 500 }
    );
  }
}