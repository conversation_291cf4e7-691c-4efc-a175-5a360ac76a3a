import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface DocumentSection {
  id: string;
  title: string;
  fields: Array<{
    name: string;
    placeholder: string;
    value: string;
    type: 'text' | 'number' | 'date' | 'email' | 'currency';
    required: boolean;
  }>;
  isComplete: boolean;
}

// Simple field type detection
function detectFieldType(fieldName: string): 'text' | 'number' | 'date' | 'email' | 'currency' {
  const name = fieldName.toLowerCase();

  if (name.includes('email')) return 'email';
  if (name.includes('date')) return 'date';
  if (name.includes('cost') || name.includes('rate') || name.includes('amount')) return 'currency';
  if (name.includes('hours') || name.includes('quantity')) return 'number';

  return 'text';
}

// Simple required field check
function isFieldRequired(fieldName: string): boolean {
  const required = ['client', 'company', 'project', 'name'];
  const name = fieldName.toLowerCase();
  return required.some(req => name.includes(req));
}

// Simple placeholder generation
function generatePlaceholder(fieldName: string): string {
  return fieldName
    .toLowerCase()
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Analyze markdown content and extract sections with fields
function analyzeMarkdownContent(markdown: string): DocumentSection[] {
  const sections: DocumentSection[] = [];
  const lines = markdown.split('\n');
  let currentSection: Partial<DocumentSection> | null = null;
  let sectionContent: string[] = [];
  let sectionId = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Detect section headers (# ## ###)
    if (line.match(/^#{1,3}\s+/)) {
      // Save previous section if exists
      if (currentSection) {
        const fields = extractFieldsFromContent(sectionContent.join('\n'));
        sections.push({
          id: `section-${sectionId++}`,
          title: currentSection.title || 'Untitled Section',
          fields: fields,
          isComplete: fields.length === 0 || fields.every(f => !f.required)
        });
      }

      // Start new section
      currentSection = {
        title: line.replace(/^#{1,3}\s+/, '').trim()
      };
      sectionContent = [line];
    } else {
      sectionContent.push(line);
    }
  }

  // Add final section
  if (currentSection) {
    const fields = extractFieldsFromContent(sectionContent.join('\n'));
    sections.push({
      id: `section-${sectionId++}`,
      title: currentSection.title || 'Untitled Section',
      fields: fields,
      isComplete: fields.length === 0 || fields.every(f => !f.required)
    });
  }

  return sections.filter(section => section.fields.length > 0);
}

// Extract field placeholders from content
function extractFieldsFromContent(content: string): Array<{
  name: string;
  placeholder: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'email' | 'currency';
  required: boolean;
}> {
  const fieldRegex = /\[([A-Z_]+)\]/g;
  const fields = new Set<string>();
  let match;

  while ((match = fieldRegex.exec(content)) !== null) {
    fields.add(match[1]);
  }

  return Array.from(fields).map(fieldName => ({
    name: fieldName,
    placeholder: generatePlaceholder(fieldName),
    value: '',
    type: detectFieldType(fieldName),
    required: isFieldRequired(fieldName)
  }));
}

// Generate HTML preview with better formatting
function generatePreview(markdown: string): string {
  return markdown
    .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold mb-6 text-gray-900 border-b-2 border-gray-200 pb-2">$1</h1>')
    .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-bold mb-4 text-gray-800 mt-8">$1</h2>')
    .replace(/^### (.*$)/gm, '<h3 class="text-xl font-bold mb-3 text-gray-700 mt-6">$3</h3>')
    .replace(/^\*\*(.*?)\*\*/gm, '<strong class="font-bold text-gray-900">$1</strong>')
    .replace(/^\*(.*?)\*/gm, '<em class="italic text-gray-700">$1</em>')
    .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">$1</li>')
    .replace(/(<li>.*<\/li>)/g, '<ul class="list-disc list-inside mb-4 space-y-1">$1</ul>')
    .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 leading-relaxed">')
    .replace(/^(?!<[h|u|l])/gm, '<p class="mb-4 text-gray-700 leading-relaxed">')
    .replace(/(?<!>)$/gm, '</p>');
}

export async function POST(request: NextRequest) {
  try {
    const { templateId } = await request.json();

    if (!templateId) {
      return NextResponse.json({ error: 'Missing template ID' }, { status: 400 });
    }

    // Load template metadata
    const uploadsDir = join(process.cwd(), 'uploads');
    const metadataPath = join(uploadsDir, `${templateId}.json`);

    let templateData;
    try {
      const metadataContent = await readFile(metadataPath, 'utf-8');
      templateData = JSON.parse(metadataContent);
    } catch (error) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    // Create simple sections
    const sections = createSimpleSections(templateData.placeholders);

    return NextResponse.json({
      sections: sections,
      preview: '<div class="p-4">Document ready for editing</div>',
      originalName: templateData.name,
      totalFields: templateData.placeholders.length
    });

  } catch (error) {
    console.error('Analysis error:', error);
    return NextResponse.json(
      { error: 'Analysis failed' },
      { status: 500 }
    );
  }
}

// Simple section creation
function createSimpleSections(placeholders: string[]): DocumentSection[] {
  const sections: DocumentSection[] = [];

  // Group fields by category
  const clientFields = placeholders.filter(p => p.includes('CLIENT'));
  const projectFields = placeholders.filter(p => p.includes('PROJECT'));
  const costFields = placeholders.filter(p => p.includes('COST') || p.includes('RATE') || p.includes('HOURS') || p.includes('DATE'));
  const otherFields = placeholders.filter(p =>
    !clientFields.includes(p) &&
    !projectFields.includes(p) &&
    !costFields.includes(p)
  );

  let sectionId = 0;

  // Create sections
  if (clientFields.length > 0 || projectFields.length > 0) {
    const allBasicFields = [...clientFields, ...projectFields];
    sections.push({
      id: `section-${sectionId++}`,
      title: 'Basic Information',
      fields: allBasicFields.map(createField),
      isComplete: false
    });
  }

  if (costFields.length > 0) {
    sections.push({
      id: `section-${sectionId++}`,
      title: 'Pricing & Timeline',
      fields: costFields.map(createField),
      isComplete: false
    });
  }

  if (otherFields.length > 0) {
    sections.push({
      id: `section-${sectionId++}`,
      title: 'Additional Details',
      fields: otherFields.map(createField),
      isComplete: false
    });
  }

  // If no fields found, create a default section
  if (sections.length === 0) {
    sections.push({
      id: 'section-0',
      title: 'Document Fields',
      fields: placeholders.map(createField),
      isComplete: false
    });
  }

  return sections;
}

// Create a field object
function createField(placeholder: string) {
  return {
    name: placeholder,
    placeholder: generatePlaceholder(placeholder),
    value: '',
    type: detectFieldType(placeholder),
    required: isFieldRequired(placeholder)
  };
}
