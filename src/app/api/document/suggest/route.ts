import { NextRequest, NextResponse } from 'next/server';

// AI-powered suggestion engine
function generateAISuggestions(changedField: string, value: string, currentSection: any, allSections: any[]): { [key: string]: string } {
  const suggestions: { [key: string]: string } = {};
  
  // Get current date for date-related suggestions
  const now = new Date();
  const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
  
  // Company-based suggestions
  if (changedField === 'CLIENT_COMPANY' && value) {
    suggestions['PROJECT_NAME'] = `${value} Digital Transformation Project`;
    suggestions['CLIENT_NAME'] = `${value} Project Manager`;
    suggestions['CLIENT_EMAIL'] = `contact@${value.toLowerCase().replace(/\s+/g, '')}.com`;
  }
  
  // Project name-based suggestions
  if (changedField === 'PROJECT_NAME' && value) {
    const projectType = inferProjectType(value);
    suggestions['PROJECT_DESCRIPTION'] = generateProjectDescription(value, projectType);
    
    // Suggest timeline based on project type
    if (projectType === 'web-application') {
      suggestions['ESTIMATED_HOURS'] = '120';
      suggestions['END_DATE'] = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 60 days
    } else if (projectType === 'mobile-application') {
      suggestions['ESTIMATED_HOURS'] = '200';
      suggestions['END_DATE'] = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 90 days
    } else if (projectType === 'e-commerce') {
      suggestions['ESTIMATED_HOURS'] = '160';
      suggestions['END_DATE'] = new Date(now.getTime() + 75 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 75 days
    }
  }
  
  // Hourly rate-based suggestions
  if (changedField === 'HOURLY_RATE' && value) {
    const rate = parseFloat(value);
    if (!isNaN(rate)) {
      // Find estimated hours to calculate total cost
      const estimatedHours = findFieldValue(allSections, 'ESTIMATED_HOURS');
      if (estimatedHours) {
        const hours = parseFloat(estimatedHours);
        if (!isNaN(hours)) {
          suggestions['TOTAL_COST'] = (rate * hours).toLocaleString();
        }
      } else {
        // Suggest typical project costs based on rate
        if (rate >= 150) {
          suggestions['ESTIMATED_HOURS'] = '80';
          suggestions['TOTAL_COST'] = (rate * 80).toLocaleString();
        } else if (rate >= 100) {
          suggestions['ESTIMATED_HOURS'] = '120';
          suggestions['TOTAL_COST'] = (rate * 120).toLocaleString();
        } else {
          suggestions['ESTIMATED_HOURS'] = '160';
          suggestions['TOTAL_COST'] = (rate * 160).toLocaleString();
        }
      }
    }
  }
  
  // Estimated hours-based suggestions
  if (changedField === 'ESTIMATED_HOURS' && value) {
    const hours = parseFloat(value);
    if (!isNaN(hours)) {
      const hourlyRate = findFieldValue(allSections, 'HOURLY_RATE');
      if (hourlyRate) {
        const rate = parseFloat(hourlyRate);
        if (!isNaN(rate)) {
          suggestions['TOTAL_COST'] = (rate * hours).toLocaleString();
        }
      }
      
      // Suggest timeline based on hours
      const daysNeeded = Math.ceil(hours / 8); // 8 hours per day
      suggestions['END_DATE'] = new Date(now.getTime() + daysNeeded * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    }
  }
  
  // Start date-based suggestions
  if (changedField === 'START_DATE' && value) {
    const startDate = new Date(value);
    const estimatedHours = findFieldValue(allSections, 'ESTIMATED_HOURS');
    if (estimatedHours) {
      const hours = parseFloat(estimatedHours);
      if (!isNaN(hours)) {
        const daysNeeded = Math.ceil(hours / 8);
        const endDate = new Date(startDate.getTime() + daysNeeded * 24 * 60 * 60 * 1000);
        suggestions['END_DATE'] = endDate.toISOString().split('T')[0];
      }
    } else {
      // Default 30-day project
      const endDate = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000);
      suggestions['END_DATE'] = endDate.toISOString().split('T')[0];
    }
  }
  
  // Auto-fill company information
  if (changedField === 'COMPANY_NAME' && value) {
    suggestions['CONTACT_NAME'] = 'Chase Vazquez';
    suggestions['CONTACT_EMAIL'] = '<EMAIL>';
    suggestions['PHONE'] = '+****************';
    suggestions['ADDRESS'] = '123 Innovation Drive, Tech City, TC 12345';
  }
  
  // Date auto-suggestions
  if (!findFieldValue(allSections, 'START_DATE')) {
    suggestions['START_DATE'] = now.toISOString().split('T')[0];
  }
  
  return suggestions;
}

function inferProjectType(projectName: string): string {
  const name = projectName.toLowerCase();
  if (name.includes('web') || name.includes('website') || name.includes('portal')) return 'web-application';
  if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) return 'mobile-application';
  if (name.includes('ecommerce') || name.includes('shop') || name.includes('store')) return 'e-commerce';
  if (name.includes('api') || name.includes('backend') || name.includes('service')) return 'api-development';
  return 'custom-development';
}

function generateProjectDescription(projectName: string, projectType: string): string {
  const descriptions: Record<string, string> = {
    'web-application': `${projectName} is a comprehensive web application featuring modern design, responsive layout, and robust functionality. This project will deliver a scalable solution with user authentication, data management, and intuitive user interface.`,
    'mobile-application': `${projectName} is a cutting-edge mobile application designed for both iOS and Android platforms. The app will feature native performance, offline capabilities, and seamless user experience across all devices.`,
    'e-commerce': `${projectName} is a full-featured e-commerce platform with product catalog, shopping cart, secure payment processing, and comprehensive admin dashboard for inventory and order management.`,
    'api-development': `${projectName} involves developing robust and scalable APIs with comprehensive documentation, authentication, rate limiting, and monitoring capabilities.`,
    'custom-development': `${projectName} is a custom software solution tailored to meet specific business requirements with modern architecture and best practices.`
  };

  return descriptions[projectType] || descriptions['custom-development'];
}

function findFieldValue(allSections: any[], fieldName: string): string | null {
  for (const section of allSections) {
    const field = section.fields.find((f: any) => f.name === fieldName);
    if (field && field.value) {
      return field.value;
    }
  }
  return null;
}

export async function POST(request: NextRequest) {
  try {
    const { changedField, value, currentSection, allSections } = await request.json();

    if (!changedField || !value || !currentSection) {
      return NextResponse.json({});
    }

    // Generate AI suggestions based on the changed field
    const suggestions = generateAISuggestions(changedField, value, currentSection, allSections);

    return NextResponse.json(suggestions);

  } catch (error) {
    console.error('AI suggestion error:', error);
    return NextResponse.json({});
  }
}
