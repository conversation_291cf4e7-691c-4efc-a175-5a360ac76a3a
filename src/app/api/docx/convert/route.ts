import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { markdown, templateId, clientName = 'Client' } = await request.json();

    if (!markdown) {
      return NextResponse.json({ error: 'Missing markdown content' }, { status: 400 });
    }

    console.log('📄 DOCX CONVERT: Starting markdown to DOCX conversion...');
    console.log('Template ID:', templateId);
    console.log('Markdown length:', markdown.length);

    // Create temporary files
    const timestamp = Date.now();
    const tempDir = path.join(process.cwd(), 'uploads');
    const markdownFile = path.join(tempDir, `temp-${timestamp}.md`);
    const docxFile = path.join(tempDir, `converted-${timestamp}.docx`);

    // Ensure uploads directory exists
    try {
      await fs.access(tempDir);
    } catch {
      await fs.mkdir(tempDir, { recursive: true });
    }

    // Write markdown to temporary file
    await fs.writeFile(markdownFile, markdown, 'utf8');

    // Convert markdown to DOCX using pandoc with reference document for format preservation
    let pandocCommand;

    if (templateId) {
      // Use original template as reference document to preserve formatting
      const originalDocxPath = path.join(tempDir, `${templateId}.docx`);

      try {
        await fs.access(originalDocxPath);
        pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${docxFile}"`;
        console.log('📄 DOCX CONVERT: Using reference document for format preservation');
      } catch {
        console.warn('📄 DOCX CONVERT: Original template not found, using basic conversion');
        pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx -o "${docxFile}"`;
      }
    } else {
      pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx -o "${docxFile}"`;
    }

    console.log('📄 DOCX CONVERT: Running pandoc command:', pandocCommand);

    try {
      await execAsync(pandocCommand);
      console.log('📄 DOCX CONVERT: Pandoc conversion successful');
    } catch (pandocError) {
      console.error('📄 DOCX CONVERT: Pandoc conversion failed:', pandocError);
      throw new Error(`Pandoc conversion failed: ${pandocError}`);
    }

    // Read the generated DOCX file
    const docxBuffer = await fs.readFile(docxFile);
    
    console.log('📄 DOCX CONVERT: DOCX file generated, size:', docxBuffer.length, 'bytes');

    // Clean up temporary files
    try {
      await fs.unlink(markdownFile);
      await fs.unlink(docxFile);
      console.log('📄 DOCX CONVERT: Temporary files cleaned up');
    } catch (cleanupError) {
      console.warn('📄 DOCX CONVERT: Failed to clean up temporary files:', cleanupError);
    }

    // Generate filename
    const sanitizedClientName = clientName.replace(/[^a-zA-Z0-9]/g, '');
    const filename = `SOW-${sanitizedClientName}-${new Date().toISOString().split('T')[0]}.docx`;

    // Return the DOCX file as a downloadable response
    return new NextResponse(docxBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': docxBuffer.length.toString(),
        'X-Filename': filename,
        'X-File-Size': docxBuffer.length.toString()
      }
    });

  } catch (error) {
    console.error('📄 DOCX CONVERT: Conversion error:', error);
    
    return NextResponse.json({
      error: 'DOCX conversion failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'DOCX Conversion API',
    description: 'Converts markdown to DOCX format using pandoc',
    endpoints: {
      'POST /api/docx/convert': {
        description: 'Convert markdown to DOCX',
        required_fields: ['markdown'],
        optional_fields: ['templateId', 'clientName'],
        response: 'DOCX file download'
      }
    },
    requirements: [
      'pandoc must be installed on the system',
      'uploads directory must be writable'
    ]
  });
}
