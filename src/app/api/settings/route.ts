import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';

interface CompanyInfo {
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  website: string;
}

interface Preferences {
  defaultProjectType: string;
  defaultBudgetRange: string;
  autoSave: boolean;
  emailNotifications: boolean;
}

interface Settings {
  companyInfo: CompanyInfo;
  preferences: Preferences;
}

const SETTINGS_FILE = 'settings.json';

// GET - Load settings
export async function GET() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, SETTINGS_FILE);

    try {
      const settingsData = await readFile(settingsPath, 'utf-8');
      const settings = JSON.parse(settingsData);
      return NextResponse.json(settings);
    } catch (error) {
      // Return default settings if file doesn't exist
      const defaultSettings: Settings = {
        companyInfo: {
          companyName: 'QuantumRhino',
          contactName: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          address: '123 Innovation Drive, Tech City, TC 12345',
          website: 'https://quantumrhino.com'
        },
        preferences: {
          defaultProjectType: 'web-application',
          defaultBudgetRange: '50000',
          autoSave: true,
          emailNotifications: true
        }
      };
      return NextResponse.json(defaultSettings);
    }
  } catch (error) {
    console.error('Settings load error:', error);
    return NextResponse.json(
      { error: 'Failed to load settings' },
      { status: 500 }
    );
  }
}

// POST - Save settings
export async function POST(request: NextRequest) {
  try {
    const settings: Settings = await request.json();

    // Validate settings structure
    if (!settings.companyInfo || !settings.preferences) {
      return NextResponse.json(
        { error: 'Invalid settings structure' },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads');
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Save settings to file
    const settingsPath = join(uploadsDir, SETTINGS_FILE);
    await writeFile(settingsPath, JSON.stringify(settings, null, 2));

    console.log('Settings saved successfully:', settings);

    return NextResponse.json({
      message: 'Settings saved successfully',
      settings
    });

  } catch (error) {
    console.error('Settings save error:', error);
    return NextResponse.json(
      { error: 'Failed to save settings: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
