import { NextRequest, NextResponse } from 'next/server';
import { AutofillService } from '../../../../lib/autofill/autofill-service';
import { MarkdownGenerator } from '../../../../lib/sow-generator/markdown-generator';
import { PandocService } from '../../../../lib/sow-generator/pandoc-service';
import fs from 'fs/promises';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      companyName, 
      projectType, 
      budget, 
      timeline, 
      clientName, 
      clientEmail, 
      projectDescription,
      outputFormat = 'markdown' // 'markdown', 'docx', 'pdf'
    } = body;

    // Validate required fields
    if (!companyName || !projectType || !budget) {
      return NextResponse.json(
        { error: 'Missing required fields: companyName, projectType, budget' },
        { status: 400 }
      );
    }

    // Generate SOW data using autofill service
    const sowData = AutofillService.generateSOW({
      companyName,
      projectType,
      budget,
      timeline,
      clientName,
      clientEmail,
      projectDescription
    });

    // Generate markdown
    const markdown = MarkdownGenerator.generateSOWMarkdown(sowData);

    // Handle different output formats
    switch (outputFormat) {
      case 'markdown':
        return NextResponse.json({
          success: true,
          data: sowData,
          markdown: markdown,
          format: 'markdown'
        });

      case 'docx':
        try {
          const docxPath = await PandocService.generateSOWDocx(markdown, companyName);
          const docxBuffer = await fs.readFile(docxPath);
          
          // Clean up temp file
          await fs.unlink(docxPath).catch(() => {});

          return new NextResponse(docxBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'Content-Disposition': `attachment; filename="SOW-${companyName.replace(/[^a-zA-Z0-9]/g, '')}-${new Date().toISOString().split('T')[0]}.docx"`
            }
          });
        } catch (error) {
          console.error('DOCX generation error:', error);
          return NextResponse.json(
            { error: 'Failed to generate DOCX file', details: String(error) },
            { status: 500 }
          );
        }

      case 'pdf':
        try {
          const pdfPath = await PandocService.generateSOWPdf(markdown, companyName);
          const pdfBuffer = await fs.readFile(pdfPath);
          
          // Clean up temp file
          await fs.unlink(pdfPath).catch(() => {});

          return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="SOW-${companyName.replace(/[^a-zA-Z0-9]/g, '')}-${new Date().toISOString().split('T')[0]}.pdf"`
            }
          });
        } catch (error) {
          console.error('PDF generation error:', error);
          return NextResponse.json(
            { error: 'Failed to generate PDF file', details: String(error) },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { error: 'Invalid output format. Supported formats: markdown, docx, pdf' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('SOW generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'SOW Generator API',
    endpoints: {
      'POST /api/sow/generate': {
        description: 'Generate a Statement of Work',
        required_fields: ['companyName', 'projectType', 'budget'],
        optional_fields: ['timeline', 'clientName', 'clientEmail', 'projectDescription', 'outputFormat'],
        output_formats: ['markdown', 'docx', 'pdf'],
        example: {
          companyName: 'Acme Corp',
          projectType: 'web-application',
          budget: '50000',
          timeline: '12 weeks',
          clientName: 'John Doe',
          clientEmail: '<EMAIL>',
          outputFormat: 'docx'
        }
      }
    }
  });
}
