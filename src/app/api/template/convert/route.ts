import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import PizZip from 'pizzip';

const execAsync = promisify(exec);

// DIRECT DOCX MODIFICATION: Modify original DOCX without pandoc conversion
async function modifyOriginalDocx(
  markdown: string,
  templateId: string
): Promise<Buffer> {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    // Read the original DOCX file
    const originalBuffer = await readFile(originalDocxPath);
    console.log('Original DOCX size:', originalBuffer.length);

    // Load the DOCX as a ZIP file
    const zip = new PizZip(originalBuffer);

    // Convert markdown to simple text for replacement
    const textContent = markdownToSimpleText(markdown);
    console.log('Converted markdown to text, length:', textContent.length);

    // Extract and modify the main document content
    const documentXml = zip.file('word/document.xml')?.asText();
    if (!documentXml) {
      throw new Error('Could not extract document.xml from DOCX file');
    }

    // Replace content while preserving all structure
    const updatedDocumentXml = replaceDocumentContent(documentXml, textContent);

    // Update the ZIP with new content
    zip.file('word/document.xml', updatedDocumentXml);

    // Generate the updated DOCX (should be identical to original except content)
    const updatedBuffer = zip.generate({ type: 'nodebuffer' });

    console.log('DIRECT DOCX MODIFICATION SUCCESSFUL - original structure preserved');
    console.log('Modified DOCX size:', updatedBuffer.length);
    return updatedBuffer;

  } catch (error) {
    console.error('Direct DOCX modification failed:', error);
    console.log('Falling back to pandoc method...');
    return simplePandocRoundTrip(markdown, templateId);
  }
}

// Convert markdown to simple text for content replacement
function markdownToSimpleText(markdown: string): string {
  return markdown
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/`(.*?)`/g, '$1') // Remove code
    .replace(/^\s*[-*+]\s+/gm, '• ') // Convert bullets
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered lists
    .trim();
}

// Replace content in document.xml while preserving structure
function replaceDocumentContent(documentXml: string, newContent: string): string {
  // Find the main content area (between <w:body> tags)
  const bodyMatch = documentXml.match(/<w:body>([\s\S]*?)<\/w:body>/);
  if (!bodyMatch) {
    throw new Error('Could not find document body in XML');
  }

  // Create new content paragraphs
  const paragraphs = newContent.split('\n\n').filter(p => p.trim());
  const newBodyContent = paragraphs.map(paragraph =>
    `<w:p><w:r><w:t>${escapeXml(paragraph)}</w:t></w:r></w:p>`
  ).join('');

  // Replace the body content
  const updatedXml = documentXml.replace(
    /<w:body>[\s\S]*?<\/w:body>/,
    `<w:body>${newBodyContent}</w:body>`
  );

  return updatedXml;
}

// Escape XML special characters
function escapeXml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

// IMPROVED PANDOC CONVERSION: Better compatibility with Pages
async function improvedPandocConversion(
  markdown: string,
  templateId: string
): Promise<Buffer> {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    // Create temporary markdown file with AI-processed content
    const tempMarkdownPath = join(uploadsDir, `ai-processed-${templateId}-${Date.now()}.md`);
    await writeFile(tempMarkdownPath, markdown, 'utf-8');

    // Create output DOCX path
    const outputDocxPath = join(uploadsDir, `pages-compatible-${templateId}-${Date.now()}.docx`);

    // Try multiple pandoc approaches for Pages compatibility
    console.log('Attempting Pages-compatible DOCX generation...');

    // First, try with minimal options (sometimes less is more for Pages)
    const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" -o "${outputDocxPath}"`;

    console.log('MINIMAL PANDOC (Pages compatibility test):', pandocCommand);
    console.log('Original DOCX size:', (await readFile(originalDocxPath)).length);

    await execAsync(pandocCommand);

    // Read the generated DOCX
    const outputBuffer = await readFile(outputDocxPath);
    console.log('Generated DOCX size:', outputBuffer.length);

    // Verify the file is a valid ZIP/DOCX (should start with PK)
    if (outputBuffer[0] !== 0x50 || outputBuffer[1] !== 0x4B) {
      console.error('Generated file is not a valid ZIP/DOCX file!');
      throw new Error('Generated DOCX file is corrupted - invalid ZIP signature');
    }

    // Clean up temporary files (keep output for debugging)
    try {
      await unlink(tempMarkdownPath);
      // Keep output file for debugging: await unlink(outputDocxPath);
      console.log('DEBUG: Keeping generated DOCX for testing:', outputDocxPath);
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }

    // Test Pages compatibility
    console.log('Testing Pages compatibility...');
    try {
      // Check DOCX structure for Pages compatibility
      const testCommand = `unzip -t "${outputDocxPath}"`;
      await execAsync(testCommand);
      console.log('✅ DOCX ZIP structure is valid');

      // Try to open with Pages (this will fail silently if Pages can't open it)
      const pagesTestCommand = `timeout 5 open -a Pages "${outputDocxPath}" || echo "Pages test completed"`;
      await execAsync(pagesTestCommand);
      console.log('✅ Pages compatibility test initiated');

    } catch (error) {
      console.warn('⚠️ Pages compatibility test failed:', error);
    }

    console.log('MINIMAL PANDOC CONVERSION SUCCESSFUL - Pages compatibility tested');
    return outputBuffer;

  } catch (error) {
    console.error('Improved pandoc conversion error:', error);
    console.log('Falling back to simple pandoc...');
    return simplePandocRoundTrip(markdown, templateId);
  }
}

// FALLBACK: SUPER SIMPLE PANDOC ROUND-TRIP: Exactly like manual pandoc commands
async function simplePandocRoundTrip(
  markdown: string,
  templateId: string
): Promise<Buffer> {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    // Create temporary markdown file with AI-processed content
    const tempMarkdownPath = join(uploadsDir, `ai-processed-${templateId}-${Date.now()}.md`);
    await writeFile(tempMarkdownPath, markdown, 'utf-8');

    // Create output DOCX path
    const outputDocxPath = join(uploadsDir, `final-sow-${templateId}-${Date.now()}.docx`);

    // EXACT SAME COMMAND AS YOUR MANUAL PANDOC COMMANDS + compatibility options
    // This should preserve 100% of the structure including logos and content control anchors
    const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${outputDocxPath}"`;

    console.log('SIMPLE PANDOC ROUND-TRIP (with compatibility options):', pandocCommand);
    console.log('Original DOCX size:', (await readFile(originalDocxPath)).length);

    await execAsync(pandocCommand);

    // Read the generated DOCX
    const outputBuffer = await readFile(outputDocxPath);
    console.log('Generated DOCX size:', outputBuffer.length);
    console.log('Generated DOCX first 50 bytes:', outputBuffer.subarray(0, 50).toString('hex'));

    // Verify the file is a valid ZIP/DOCX (should start with PK)
    if (outputBuffer[0] !== 0x50 || outputBuffer[1] !== 0x4B) {
      console.error('Generated file is not a valid ZIP/DOCX file!');
      throw new Error('Generated DOCX file is corrupted - invalid ZIP signature');
    }

    // DEBUG: Save the generated file for manual inspection
    const debugOutputPath = join(uploadsDir, `debug-generated-${templateId}-${Date.now()}.docx`);
    await writeFile(debugOutputPath, outputBuffer);
    console.log('DEBUG: Saved generated DOCX for inspection:', debugOutputPath);

    // Test if the generated DOCX can be opened manually
    try {
      await execAsync(`file "${debugOutputPath}"`);
      console.log('Generated DOCX file type verification passed');
    } catch (error) {
      console.warn('Generated DOCX file type verification failed:', error);
    }

    // Clean up temporary files (keep for debugging)
    try {
      await unlink(tempMarkdownPath);
      // Keep the output file for debugging: await unlink(outputDocxPath);
      console.log('DEBUG: Keeping output file for analysis:', outputDocxPath);
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }

    console.log('SIMPLE PANDOC ROUND-TRIP SUCCESSFUL - valid DOCX file generated');
    return outputBuffer;

  } catch (error) {
    console.error('Simple pandoc round-trip error:', error);
    throw new Error('Simple pandoc round-trip failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}



export async function POST(request: NextRequest) {
  try {
    const { templateId, markdown } = await request.json();

    if (!templateId || !markdown) {
      return NextResponse.json({ error: 'Missing template ID or markdown' }, { status: 400 });
    }

    console.log('🎯 STREAMLINED PANDOC CONVERSION - template ID:', templateId);
    console.log('Gemini-processed markdown length:', markdown.length);

    // Direct conversion - Gemini has already handled all formatting
    const docxBuffer = await improvedPandocConversion(markdown, templateId);

    // Validate the generated DOCX buffer
    if (!docxBuffer || docxBuffer.length === 0) {
      throw new Error('Generated DOCX buffer is empty');
    }

    console.log('Final DOCX buffer size for download:', docxBuffer.length);
    console.log('Final DOCX buffer first 50 bytes:', docxBuffer.subarray(0, 50).toString('hex'));

    // Return the file with proper headers and additional compatibility headers
    return new NextResponse(docxBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="SOW-Generated-${new Date().toISOString().split('T')[0]}.docx"`,
        'Content-Length': docxBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Super simple pandoc round-trip error:', error);
    return NextResponse.json(
      { error: 'Super simple pandoc round-trip failed: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
