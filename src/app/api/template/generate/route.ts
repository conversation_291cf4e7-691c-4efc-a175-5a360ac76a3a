import { NextRequest, NextResponse } from 'next/server';

interface FormData {
  clientCompany: string;
  clientName: string;
  clientEmail: string;
  projectName: string;
  hourlyRate: number;
  estimatedHours: number;
  totalCost: number;
  startDate: string;
  endDate: string;
  [key: string]: any;
}

// AI-powered content generation based on form data
function generateAIContent(formData: FormData): any {
  const projectType = inferProjectType(formData.projectName);
  const complexity = inferComplexity(formData.totalCost, formData.estimatedHours);
  
  return {
    projectDescription: generateProjectDescription(formData, projectType),
    deliverables: generateDeliverables(projectType, complexity),
    phases: generatePhases(projectType, formData.estimatedHours),
    riskMitigation: generateRiskMitigation(projectType),
    successCriteria: generateSuccessCriteria(projectType),
    additionalTerms: generateAdditionalTerms(formData.totalCost)
  };
}

function inferProjectType(projectName: string): string {
  const name = projectName.toLowerCase();
  if (name.includes('web') || name.includes('website') || name.includes('portal')) return 'web-application';
  if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) return 'mobile-application';
  if (name.includes('ecommerce') || name.includes('shop') || name.includes('store')) return 'e-commerce';
  if (name.includes('api') || name.includes('backend') || name.includes('service')) return 'api-development';
  return 'custom-development';
}

function inferComplexity(totalCost: number, hours: number): 'simple' | 'medium' | 'complex' {
  if (totalCost < 10000 || hours < 50) return 'simple';
  if (totalCost < 50000 || hours < 200) return 'medium';
  return 'complex';
}

function generateProjectDescription(formData: FormData, projectType: string): string {
  const typeDescriptions: Record<string, string> = {
    'web-application': `${formData.projectName} is a comprehensive web application designed to meet the specific needs of ${formData.clientCompany}. This project will deliver a modern, responsive, and user-friendly web solution that enhances business operations and user experience.`,
    'mobile-application': `${formData.projectName} is a cutting-edge mobile application that will provide ${formData.clientCompany} with a powerful tool to engage users and streamline business processes across iOS and Android platforms.`,
    'e-commerce': `${formData.projectName} is a robust e-commerce platform that will enable ${formData.clientCompany} to sell products/services online with a seamless shopping experience, secure payment processing, and comprehensive management tools.`,
    'api-development': `${formData.projectName} involves the development of scalable and secure APIs that will enable ${formData.clientCompany} to integrate systems, share data efficiently, and build a robust technical infrastructure.`,
    'custom-development': `${formData.projectName} is a custom software solution tailored specifically for ${formData.clientCompany}'s unique requirements and business objectives.`
  };

  return typeDescriptions[projectType] || typeDescriptions['custom-development'];
}

function generateDeliverables(projectType: string, complexity: string): string[] {
  const baseDeliverables = [
    'Project planning and requirements documentation',
    'Technical architecture and design specifications',
    'Development and implementation',
    'Quality assurance and testing',
    'Deployment and launch support',
    'Documentation and training materials'
  ];

  const typeSpecificDeliverables: Record<string, string[]> = {
    'web-application': ['Responsive web application', 'Admin dashboard', 'User authentication system'],
    'mobile-application': ['iOS application', 'Android application', 'App store submission support'],
    'e-commerce': ['Product catalog management', 'Shopping cart and checkout', 'Payment gateway integration', 'Order management system'],
    'api-development': ['RESTful API endpoints', 'API documentation', 'Authentication and security implementation'],
    'custom-development': ['Custom feature development', 'Integration with existing systems']
  };

  const complexityDeliverables: Record<string, string[]> = {
    'simple': [],
    'medium': ['Performance optimization', 'Security audit'],
    'complex': ['Performance optimization', 'Security audit', 'Load testing', 'Scalability planning', 'Maintenance documentation']
  };

  return [
    ...baseDeliverables,
    ...(typeSpecificDeliverables[projectType] || []),
    ...complexityDeliverables[complexity]
  ];
}

function generatePhases(projectType: string, totalHours: number): Array<{name: string, duration: string, description: string}> {
  const phaseDuration = Math.ceil(totalHours / 4); // Divide into 4 main phases
  
  return [
    {
      name: 'Discovery & Planning',
      duration: `${Math.ceil(phaseDuration * 0.2)} hours`,
      description: 'Requirements gathering, technical planning, and project setup'
    },
    {
      name: 'Design & Architecture',
      duration: `${Math.ceil(phaseDuration * 0.3)} hours`,
      description: 'System design, UI/UX design, and technical architecture'
    },
    {
      name: 'Development & Implementation',
      duration: `${Math.ceil(phaseDuration * 0.4)} hours`,
      description: 'Core development, feature implementation, and integration'
    },
    {
      name: 'Testing & Deployment',
      duration: `${Math.ceil(phaseDuration * 0.1)} hours`,
      description: 'Quality assurance, testing, deployment, and launch support'
    }
  ];
}

function generateRiskMitigation(projectType: string): string[] {
  return [
    'Regular communication and progress updates',
    'Agile development methodology with iterative feedback',
    'Comprehensive testing at each development phase',
    'Backup and version control systems',
    'Clear change management process'
  ];
}

function generateSuccessCriteria(projectType: string): string[] {
  const baseCriteria = [
    'Successful deployment to production environment',
    'All specified features functioning as designed',
    'Performance meets or exceeds requirements',
    'Client approval and sign-off on deliverables'
  ];

  const typeCriteria: Record<string, string[]> = {
    'web-application': ['Cross-browser compatibility achieved', 'Mobile responsiveness verified'],
    'mobile-application': ['App store approval obtained', 'Performance benchmarks met'],
    'e-commerce': ['Payment processing fully functional', 'Security compliance verified'],
    'api-development': ['API endpoints responding within SLA', 'Documentation complete and accurate']
  };

  return [...baseCriteria, ...(typeCriteria[projectType] || [])];
}

function generateAdditionalTerms(totalCost: number): string[] {
  const baseTerms = [
    'Payment terms: Net 30 days from invoice date',
    'All work performed remotely unless otherwise specified',
    'Client responsible for providing necessary access and materials',
    'Changes to scope require written approval and may affect timeline/cost'
  ];

  if (totalCost > 25000) {
    baseTerms.push('Payment schedule: 50% upfront, 50% upon completion');
    baseTerms.push('Warranty: 90 days post-launch support included');
  }

  return baseTerms;
}

// Fill template with form data and AI-generated content
function fillTemplate(markdownTemplate: string, formData: FormData, aiContent: any): string {
  let filledTemplate = markdownTemplate;
  
  // Replace basic form fields
  const replacements = {
    'CLIENT_COMPANY': formData.clientCompany,
    'CLIENT_NAME': formData.clientName,
    'CLIENT_EMAIL': formData.clientEmail,
    'PROJECT_NAME': formData.projectName,
    'START_DATE': new Date(formData.startDate).toLocaleDateString(),
    'END_DATE': new Date(formData.endDate).toLocaleDateString(),
    'HOURLY_RATE': formData.hourlyRate.toString(),
    'ESTIMATED_HOURS': formData.estimatedHours.toString(),
    'TOTAL_COST': formData.totalCost.toLocaleString(),
    'DATE': new Date().toLocaleDateString()
  };

  // Replace placeholders
  for (const [key, value] of Object.entries(replacements)) {
    const regex = new RegExp(`\\[${key}\\]`, 'g');
    filledTemplate = filledTemplate.replace(regex, value);
  }

  // Add AI-generated content sections if they don't exist
  if (!filledTemplate.includes('## Project Description')) {
    filledTemplate += `\n\n## Project Description\n${aiContent.projectDescription}`;
  }

  if (!filledTemplate.includes('## Deliverables')) {
    filledTemplate += `\n\n## Deliverables\n${aiContent.deliverables.map((d: string) => `- ${d}`).join('\n')}`;
  }

  if (!filledTemplate.includes('## Project Phases')) {
    filledTemplate += `\n\n## Project Phases\n${aiContent.phases.map((p: any) =>
      `### ${p.name} (${p.duration})\n${p.description}`
    ).join('\n\n')}`;
  }

  return filledTemplate;
}

export async function POST(request: NextRequest) {
  try {
    const { templateId, formData, markdownTemplate } = await request.json();

    if (!templateId || !formData || !markdownTemplate) {
      return NextResponse.json({ error: 'Missing required data' }, { status: 400 });
    }

    // Generate AI content based on form data
    const aiContent = generateAIContent(formData);

    // Fill template with form data and AI content
    const filledMarkdown = fillTemplate(markdownTemplate, formData, aiContent);

    // Return the completed SOW
    return NextResponse.json({
      id: templateId,
      markdown: filledMarkdown,
      formData: formData,
      aiContent: aiContent,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Template generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate SOW' },
      { status: 500 }
    );
  }
}
