import { NextRequest, NextResponse } from 'next/server';
import { readFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Get template preview (markdown content)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const uploadsDir = join(process.cwd(), 'uploads');
    
    // Try to load from metadata first
    const metadataPath = join(uploadsDir, `${templateId}.json`);
    try {
      const metadataContent = await readFile(metadataPath, 'utf-8');
      const metadata = JSON.parse(metadataContent);
      
      return NextResponse.json({
        id: templateId,
        name: metadata.name,
        markdown: metadata.originalMarkdown,
        fields: metadata.placeholders,
        uploadDate: metadata.uploadDate
      });
    } catch (metadataError) {
      // Fallback: convert DOCX to markdown on-the-fly
      const docxPath = join(uploadsDir, `${templateId}.docx`);
      
      try {
        // Check if DOCX file exists
        await readFile(docxPath);
        
        // Convert to markdown using pandoc
        const tempMarkdownPath = join(uploadsDir, `preview-${templateId}-${Date.now()}.md`);
        const pandocCommand = `pandoc "${docxPath}" -t markdown -o "${tempMarkdownPath}"`;
        
        await execAsync(pandocCommand);
        const markdown = await readFile(tempMarkdownPath, 'utf-8');
        
        // Clean up temp file
        try {
          await unlink(tempMarkdownPath);
        } catch (error) {
          console.warn('Failed to clean up temp preview file:', error);
        }
        
        // Extract placeholders
        const placeholders = new Set<string>();
        const patterns = [
          /\{([A-Z_]+)\}/g,
          /\[([A-Z_]+)\]/g,
          /\$\{([A-Z_]+)\}/g,
          /\{\{([A-Z_]+)\}\}/g,
        ];

        patterns.forEach(pattern => {
          let match;
          while ((match = pattern.exec(markdown)) !== null) {
            placeholders.add(match[1]);
          }
        });
        
        return NextResponse.json({
          id: templateId,
          name: `Template ${templateId}`,
          markdown: markdown,
          fields: Array.from(placeholders),
          uploadDate: new Date().toISOString()
        });
        
      } catch (docxError) {
        return NextResponse.json(
          { error: 'Template file not found' },
          { status: 404 }
        );
      }
    }

  } catch (error) {
    console.error('Template preview error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
