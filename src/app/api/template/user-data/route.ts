import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { SavedUserData } from '../../../../lib/types/template';

interface TemplateUserDataFile {
  templateId: string;
  userData: SavedUserData;
  lastSaved: string;
  version: number;
}

// Get user data directory path
function getUserDataDir(): string {
  return join(process.cwd(), 'uploads', 'user-data');
}

// Get user data file path for a template
function getUserDataFilePath(templateId: string): string {
  return join(getUserDataDir(), `${templateId}-userdata.json`);
}

// Ensure user data directory exists
async function ensureUserDataDir(): Promise<void> {
  try {
    await mkdir(getUserDataDir(), { recursive: true });
  } catch (error) {
    // Directory might already exist, ignore error
  }
}

// GET - Load user data for a template
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    const filePath = getUserDataFilePath(templateId);

    try {
      const fileContent = await readFile(filePath, 'utf-8');
      const data: TemplateUserDataFile = JSON.parse(fileContent);
      
      return NextResponse.json({
        templateId: data.templateId,
        userData: data.userData,
        lastSaved: data.lastSaved,
        version: data.version
      });
    } catch (error) {
      // File doesn't exist or is corrupted
      return NextResponse.json({ 
        templateId,
        userData: null,
        message: 'No saved user data found'
      });
    }
  } catch (error) {
    console.error('Failed to load user data:', error);
    return NextResponse.json(
      { error: 'Failed to load user data' },
      { status: 500 }
    );
  }
}

// POST - Save user data for a template
export async function POST(request: NextRequest) {
  try {
    const { templateId, userData } = await request.json();

    if (!templateId || !userData) {
      return NextResponse.json(
        { error: 'Template ID and user data are required' },
        { status: 400 }
      );
    }

    // Ensure directory exists
    await ensureUserDataDir();

    // Load existing data to get version
    let existingVersion = 0;
    const filePath = getUserDataFilePath(templateId);
    
    try {
      const existingContent = await readFile(filePath, 'utf-8');
      const existingData: TemplateUserDataFile = JSON.parse(existingContent);
      existingVersion = existingData.version || 0;
    } catch (error) {
      // File doesn't exist, start with version 0
    }

    // Create new data structure
    const templateUserData: TemplateUserDataFile = {
      templateId,
      userData: {
        ...userData,
        lastSaved: new Date(),
        version: existingVersion + 1
      },
      lastSaved: new Date().toISOString(),
      version: existingVersion + 1
    };

    // Save to file
    await writeFile(filePath, JSON.stringify(templateUserData, null, 2));

    return NextResponse.json({
      message: 'User data saved successfully',
      templateId,
      version: templateUserData.version,
      lastSaved: templateUserData.lastSaved
    });
  } catch (error) {
    console.error('Failed to save user data:', error);
    return NextResponse.json(
      { error: 'Failed to save user data' },
      { status: 500 }
    );
  }
}

// DELETE - Delete user data for a template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    const filePath = getUserDataFilePath(templateId);

    try {
      // Try to delete the file
      const fs = await import('fs/promises');
      await fs.unlink(filePath);
      
      return NextResponse.json({
        message: 'User data deleted successfully',
        templateId
      });
    } catch (error) {
      // File might not exist, which is fine
      return NextResponse.json({
        message: 'No user data found to delete',
        templateId
      });
    }
  } catch (error) {
    console.error('Failed to delete user data:', error);
    return NextResponse.json(
      { error: 'Failed to delete user data' },
      { status: 500 }
    );
  }
}

// PUT - Update specific fields in user data
export async function PUT(request: NextRequest) {
  try {
    const { templateId, updates } = await request.json();

    if (!templateId || !updates) {
      return NextResponse.json(
        { error: 'Template ID and updates are required' },
        { status: 400 }
      );
    }

    const filePath = getUserDataFilePath(templateId);

    // Load existing data
    let existingData: TemplateUserDataFile;
    try {
      const fileContent = await readFile(filePath, 'utf-8');
      existingData = JSON.parse(fileContent);
    } catch (error) {
      // Create new data if file doesn't exist
      existingData = {
        templateId,
        userData: {},
        lastSaved: new Date().toISOString(),
        version: 0
      };
    }

    // Merge updates with existing data
    const updatedUserData = {
      ...existingData.userData,
      ...updates,
      lastSaved: new Date(),
      version: (existingData.version || 0) + 1
    };

    const templateUserData: TemplateUserDataFile = {
      templateId,
      userData: updatedUserData,
      lastSaved: new Date().toISOString(),
      version: (existingData.version || 0) + 1
    };

    // Ensure directory exists
    await ensureUserDataDir();

    // Save updated data
    await writeFile(filePath, JSON.stringify(templateUserData, null, 2));

    return NextResponse.json({
      message: 'User data updated successfully',
      templateId,
      version: templateUserData.version,
      lastSaved: templateUserData.lastSaved
    });
  } catch (error) {
    console.error('Failed to update user data:', error);
    return NextResponse.json(
      { error: 'Failed to update user data' },
      { status: 500 }
    );
  }
}
