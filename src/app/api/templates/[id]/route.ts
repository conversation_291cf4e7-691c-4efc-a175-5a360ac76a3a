import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface SavedTemplate {
  id: string;
  name: string;
  markdown: string;
  sections: any[];
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
  savedFormData?: any;
}

async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const templatesPath = join(uploadsDir, 'saved-templates.json');
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Load all templates
    const templates = await loadTemplates();
    
    // Find the specific template
    const template = templates.find(t => t.id === id);
    
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    // Update last used timestamp and usage count
    template.lastUsed = new Date().toISOString();
    template.usageCount = (template.usageCount || 0) + 1;

    // Save updated templates back to file
    try {
      const { writeFile } = await import('fs/promises');
      const uploadsDir = join(process.cwd(), 'uploads');
      const templatesPath = join(uploadsDir, 'saved-templates.json');
      await writeFile(templatesPath, JSON.stringify(templates, null, 2));
    } catch (error) {
      console.warn('Failed to update template usage stats:', error);
    }

    return NextResponse.json(template);

  } catch (error) {
    console.error('Template retrieval error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve template' },
      { status: 500 }
    );
  }
}
