import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, rename } from 'fs/promises';
import { join } from 'path';

export async function PUT(request: NextRequest) {
  try {
    const { templateId, newName } = await request.json();

    if (!templateId || !newName) {
      return NextResponse.json(
        { error: 'Template ID and new name are required' },
        { status: 400 }
      );
    }

    const uploadsDir = join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    // Read current templates list
    let templates = [];
    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      templates = JSON.parse(templatesData);
    } catch (error) {
      console.error('Failed to read templates list:', error);
      return NextResponse.json(
        { error: 'Failed to read templates list' },
        { status: 500 }
      );
    }

    // Find the template to rename
    const templateIndex = templates.findIndex((t: any) => t.id === templateId);
    if (templateIndex === -1) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    const template = templates[templateIndex];
    const oldName = template.name;

    // Validate that the new name has the same extension
    const oldExtension = oldName.split('.').pop()?.toLowerCase();
    const newExtension = newName.split('.').pop()?.toLowerCase();
    
    if (oldExtension !== newExtension) {
      return NextResponse.json(
        { error: `File extension must remain .${oldExtension}` },
        { status: 400 }
      );
    }

    // Check if a template with the new name already exists
    const nameExists = templates.some((t: any, index: number) => 
      index !== templateIndex && t.name.toLowerCase() === newName.toLowerCase()
    );
    
    if (nameExists) {
      return NextResponse.json(
        { error: 'A template with this name already exists' },
        { status: 400 }
      );
    }

    // Rename the physical files
    const oldDocxPath = join(uploadsDir, `${templateId}.docx`);
    const newDocxPath = join(uploadsDir, `${templateId}.docx`); // Keep same file, just update metadata
    const oldMarkdownPath = join(uploadsDir, `${templateId}.md`);
    const newMarkdownPath = join(uploadsDir, `${templateId}.md`); // Keep same file, just update metadata

    // Update the template in the list
    templates[templateIndex] = {
      ...template,
      name: newName
    };

    // Save updated templates list
    await writeFile(templatesListPath, JSON.stringify(templates, null, 2));

    console.log(`Template renamed from "${oldName}" to "${newName}"`);

    return NextResponse.json({
      success: true,
      message: 'Template renamed successfully',
      template: templates[templateIndex]
    });

  } catch (error) {
    console.error('Template rename error:', error);
    return NextResponse.json(
      { error: 'Failed to rename template: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
