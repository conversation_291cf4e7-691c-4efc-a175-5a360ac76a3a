'use client';

import React, { useState, useEffect } from 'react';
import Input from '../ui/input';
import Textarea from '../ui/textarea';

interface PricingFormData {
    budget: string;
    paymentTerms: string;
    notes: string;
}

interface PricingFormProps {
    onChange?: (data: PricingFormData) => void;
}

const PricingForm: React.FC<PricingFormProps> = ({ onChange }) => {
    const [budget, setBudget] = useState('');
    const [paymentTerms, setPaymentTerms] = useState('');
    const [notes, setNotes] = useState('');

    useEffect(() => {
        if (onChange) {
            onChange({ budget, paymentTerms, notes });
        }
    }, [budget, paymentTerms, notes, onChange]);

    return (
        <div className="pricing-form">
            <h2>Pricing Information</h2>
            <Input
                type="text"
                placeholder="Enter budget"
                value={budget}
                onChange={(e) => setBudget(e.target.value)}
            />
            <Input
                type="text"
                placeholder="Enter payment terms"
                value={paymentTerms}
                onChange={(e) => setPaymentTerms(e.target.value)}
            />
            <Textarea
                placeholder="Additional notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
            />
        </div>
    );
};

export default PricingForm;