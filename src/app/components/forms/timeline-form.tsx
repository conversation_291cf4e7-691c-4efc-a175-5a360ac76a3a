'use client';

import React, { useState, useEffect } from 'react';
import Button from '../ui/button';
import Input from '../ui/input';

interface Milestone {
    title: string;
    date: string;
}

interface TimelineFormData {
    milestones: Milestone[];
}

interface TimelineFormProps {
    onChange?: (data: TimelineFormData) => void;
}

const TimelineForm: React.FC<TimelineFormProps> = ({ onChange }) => {
    const [milestones, setMilestones] = useState<Milestone[]>([{ title: '', date: '' }]);

    useEffect(() => {
        if (onChange) {
            onChange({ milestones });
        }
    }, [milestones, onChange]);

    const handleChange = (index: number, field: keyof Milestone, value: string) => {
        const newMilestones = [...milestones];
        newMilestones[index][field] = value;
        setMilestones(newMilestones);
    };

    const addMilestone = () => {
        setMilestones([...milestones, { title: '', date: '' }]);
    };

    const removeMilestone = (index: number) => {
        const newMilestones = milestones.filter((_, i) => i !== index);
        setMilestones(newMilestones);
    };

    return (
        <div>
            <h2>Project Timeline</h2>
            {milestones.map((milestone, index) => (
                <div key={index} className="milestone">
                    <Input
                        placeholder="Milestone Title"
                        value={milestone.title}
                        onChange={(e) => handleChange(index, 'title', e.target.value)}
                    />
                    <Input
                        type="date"
                        value={milestone.date}
                        onChange={(e) => handleChange(index, 'date', e.target.value)}
                    />
                    <Button onClick={() => removeMilestone(index)}>Remove</Button>
                </div>
            ))}
            <Button onClick={addMilestone}>Add Milestone</Button>
        </div>
    );
};

export default TimelineForm;