'use client';

import React from 'react';
import useSOW from '../../../hooks/use-sow';
import { generateSOWPDF } from '../../../lib/pdf/generator';

const PDFGenerator: React.FC = () => {
    const { sowData } = useSOW();

    const handleGeneratePdf = async () => {
        if (!sowData) {
            console.error('No SOW data available');
            return;
        }
        
        try {
            const pdfBuffer = await generateSOWPDF(sowData);
            const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'Statement_of_Work.pdf');
            document.body.appendChild(link);
            link.click();
            link.remove();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error generating PDF:', error);
        }
    };

    return (
        <div>
            <button onClick={handleGeneratePdf} className="btn btn-primary">
                Generate PDF
            </button>
        </div>
    );
};

export default PDFGenerator;