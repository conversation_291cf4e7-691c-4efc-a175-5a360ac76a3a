'use client';

import React from 'react';
import useTemplates from '../../../hooks/use-templates';
import TemplateCard from '../../templates/components/template-card';

const SOWTemplate = () => {
    const { templates, loading, error } = useTemplates();

    if (loading) {
        return <div>Loading templates...</div>;
    }

    if (error) {
        return <div>Error loading templates: {error}</div>;
    }

    return (
        <div className="sow-template">
            <h2>Select a Statement of Work Template</h2>
            <div className="template-gallery">
                {templates.map(template => (
                    <TemplateCard
                        key={template.id}
                        title={template.name || (template as any).title || 'Untitled Template'}
                        description={template.description}
                        onSelect={() => console.log('Selected:', template)}
                    />
                ))}
            </div>
        </div>
    );
};

export default SOWTemplate;