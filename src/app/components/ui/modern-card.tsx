import React from 'react';

interface ModernCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'gradient' | 'elevated' | 'outlined';
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  hover = true,
  padding = 'md',
  className = '',
  onClick
}) => {
  const baseClasses = 'rounded-3xl transition-all duration-500';

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-12'
  };

  const variantClasses = {
    default: 'bg-white shadow-lg border border-gray-100',
    glass: 'bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl',
    gradient: 'bg-gradient-to-br from-white/20 via-white/10 to-transparent backdrop-blur-xl border border-white/20 shadow-2xl',
    elevated: 'bg-white shadow-2xl border border-gray-50',
    outlined: 'bg-transparent border-2 border-gray-200 hover:border-gray-300'
  };

  const hoverClasses = hover ? 'hover:scale-[1.02] hover:shadow-2xl cursor-pointer' : '';

  return (
    <div
      className={`
        ${baseClasses}
        ${paddingClasses[padding]}
        ${variantClasses[variant]}
        ${hoverClasses}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default ModernCard;
