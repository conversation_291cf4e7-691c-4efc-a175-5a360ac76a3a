import React from 'react';

interface ProgressBarProps {
  progress: number; // 0-100
  variant?: 'default' | 'gradient' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  label?: string;
  animated?: boolean;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  variant = 'default',
  size = 'md',
  showPercentage = true,
  label,
  animated = true,
  className = ''
}) => {
  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  };

  const containerClasses = {
    default: 'bg-gray-200 rounded-full overflow-hidden',
    gradient: 'bg-gray-200 rounded-full overflow-hidden',
    glass: 'bg-white/20 backdrop-blur-sm rounded-full overflow-hidden border border-white/30',
    minimal: 'bg-gray-100 rounded-full overflow-hidden'
  };

  const barClasses = {
    default: 'bg-blue-600 h-full transition-all duration-500 ease-out',
    gradient: 'bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-full transition-all duration-500 ease-out',
    glass: 'bg-white/60 backdrop-blur-sm h-full transition-all duration-500 ease-out',
    minimal: 'bg-blue-500 h-full transition-all duration-500 ease-out'
  };

  const animationClasses = animated ? 'animate-shimmer' : '';

  return (
    <div className={`space-y-2 ${className}`}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center">
          {label && (
            <span className="text-sm font-medium text-gray-700">
              {label}
            </span>
          )}
          {showPercentage && (
            <span className="text-sm font-medium text-gray-600">
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}
      
      <div className={`${containerClasses[variant]} ${sizeClasses[size]}`}>
        <div
          className={`${barClasses[variant]} ${animationClasses}`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
