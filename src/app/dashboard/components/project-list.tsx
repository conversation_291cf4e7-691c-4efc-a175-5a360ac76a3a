'use client';

import React from 'react';
import Card from '../../components/ui/card';

interface Project {
  id: string;
  name: string;
  client: string;
  status: string;
  date: string;
}

interface ProjectListProps {
  projects: Project[];
}

const ProjectList: React.FC<ProjectListProps> = ({ projects }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {projects.map((project) => (
        <Card 
          key={project.id} 
          title={project.name}
          content={
            <div>
              <p><strong>Client:</strong> {project.client}</p>
              <p><strong>Status:</strong> {project.status}</p>
              <p><strong>Date:</strong> {project.date}</p>
            </div>
          }
          className="project-card"
        />
      ))}
    </div>
  );
};

export default ProjectList;