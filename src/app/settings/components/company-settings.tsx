'use client';

import React, { useState } from 'react';
import Input from '../../components/ui/input';
import Button from '../../components/ui/button';
import Card from '../../components/ui/card';

const CompanySettings = () => {
    const [companyName, setCompanyName] = useState('');
    const [companyAddress, setCompanyAddress] = useState('');
    const [companyEmail, setCompanyEmail] = useState('');
    const [companyPhone, setCompanyPhone] = useState('');

    const handleSave = () => {
        // Logic to save company settings
        console.log('Company settings saved:', {
            companyName,
            companyAddress,
            companyEmail,
            companyPhone,
        });
    };

    return (
        <Card 
            title="Company Settings"
            content={
                <div className="space-y-4">
                    <Input
                        label="Company Name"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        placeholder="Enter company name"
                    />
                    <Input
                        label="Company Address"
                        value={companyAddress}
                        onChange={(e) => setCompanyAddress(e.target.value)}
                        placeholder="Enter company address"
                    />
                    <Input
                        label="Company Email"
                        value={companyEmail}
                        onChange={(e) => setCompanyEmail(e.target.value)}
                        placeholder="Enter company email"
                        type="email"
                    />
                    <Input
                        label="Company Phone"
                        value={companyPhone}
                        onChange={(e) => setCompanyPhone(e.target.value)}
                        placeholder="Enter company phone"
                        type="tel"
                    />
                </div>
            }
            footer={<Button onClick={handleSave}>Save Settings</Button>}
        />
    );
};

export default CompanySettings;