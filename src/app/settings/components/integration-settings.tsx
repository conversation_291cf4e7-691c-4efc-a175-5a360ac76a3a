'use client';

import React, { useState } from 'react';
import Button from '../../components/ui/button';
import Input from '../../components/ui/input';
import Select from '../../components/ui/select';
import Card from '../../components/ui/card';

const IntegrationSettings = () => {
    const [integrationType, setIntegrationType] = useState('');
    const [apiKey, setApiKey] = useState('');
    const [endpointUrl, setEndpointUrl] = useState('');

    const handleSave = () => {
        // Logic to save integration settings
        console.log('Integration settings saved:', { integrationType, apiKey, endpointUrl });
    };

    return (
        <Card 
            title="Integration Settings"
            content={
                <div className="space-y-4">
                    <Select
                        options={[
                            { value: 'slack', label: 'Slack' },
                            { value: 'jira', label: 'Jira' },
                            { value: 'github', label: 'GitHub' },
                        ]}
                        value={integrationType}
                        onChange={(value: string) => setIntegrationType(value)}
                        placeholder="Select Integration Type"
                    />
                    <Input
                        label="API Key"
                        type="password"
                        placeholder="Enter API Key"
                        value={apiKey}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiKey(e.target.value)}
                    />
                    <Input
                        label="Endpoint URL"
                        type="text"
                        placeholder="Enter Endpoint URL"
                        value={endpointUrl}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEndpointUrl(e.target.value)}
                    />
                </div>
            }
            footer={<Button onClick={handleSave}>Save Settings</Button>}
        />
    );
};

export default IntegrationSettings;