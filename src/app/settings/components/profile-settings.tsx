'use client';

import React, { useState } from 'react';
import Button from '../../components/ui/button';
import Input from '../../components/ui/input';
import Textarea from '../../components/ui/textarea';

const ProfileSettings = () => {
    const [profileData, setProfileData] = useState({
        name: '',
        email: '',
        bio: '',
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setProfileData({
            ...profileData,
            [name]: value,
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Logic to save profile data goes here
        console.log('Profile data submitted:', profileData);
    };

    return (
        <div className="profile-settings">
            <h2>Profile Settings</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
                <Input
                    label="Full Name"
                    name="name"
                    value={profileData.name}
                    onChange={handleChange}
                    placeholder="Enter your full name"
                />
                <Input
                    label="Email Address"
                    name="email"
                    type="email"
                    value={profileData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                />
                <Textarea
                    label="Bio"
                    name="bio"
                    value={profileData.bio}
                    onChange={handleChange}
                    placeholder="Tell us about yourself"
                />
                <Button type="submit">Save Changes</Button>
            </form>
        </div>
    );
};

export default ProfileSettings;