'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

const SettingsPage = () => {
    const [companyInfo, setCompanyInfo] = useState({
        companyName: 'QuantumRhino',
        contactName: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Innovation Drive, Tech City, TC 12345',
        website: 'https://quantumrhino.com'
    });



    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');

    // Load settings on component mount
    useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            const response = await fetch('/api/settings');
            if (response.ok) {
                const settings = await response.json();
                setCompanyInfo(settings.companyInfo);
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    };

    const saveSettings = async () => {
        setLoading(true);
        setMessage('');

        try {
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    companyInfo
                }),
            });

            if (response.ok) {
                setMessage('Settings saved successfully!');
                setTimeout(() => setMessage(''), 3000);
            } else {
                throw new Error('Failed to save settings');
            }
        } catch (error) {
            setMessage('Failed to save settings. Please try again.');
            setTimeout(() => setMessage(''), 3000);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen relative overflow-hidden">


            <div className="relative z-10 container mx-auto px-6 py-24">


                <div className="max-w-4xl mx-auto space-y-8">
                    {/* Company Information */}
                    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                        <div className="flex items-center space-x-3 mb-6">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-white">Company Information</h2>
                                <p className="text-slate-300">This information will be used in all generated SOWs</p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-white mb-3">Company Name</label>
                                <input
                                    type="text"
                                    value={companyInfo.companyName}
                                    onChange={(e) => setCompanyInfo({...companyInfo, companyName: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-white mb-3">Contact Name</label>
                                <input
                                    type="text"
                                    value={companyInfo.contactName}
                                    onChange={(e) => setCompanyInfo({...companyInfo, contactName: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-white mb-3">Email</label>
                                <input
                                    type="email"
                                    value={companyInfo.email}
                                    onChange={(e) => setCompanyInfo({...companyInfo, email: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-white mb-3">Phone</label>
                                <input
                                    type="tel"
                                    value={companyInfo.phone}
                                    onChange={(e) => setCompanyInfo({...companyInfo, phone: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-white mb-3">Address</label>
                                <input
                                    type="text"
                                    value={companyInfo.address}
                                    onChange={(e) => setCompanyInfo({...companyInfo, address: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-white mb-3">Website</label>
                                <input
                                    type="url"
                                    value={companyInfo.website}
                                    onChange={(e) => setCompanyInfo({...companyInfo, website: e.target.value})}
                                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                                />
                            </div>
                        </div>
                    </div>



                    {/* Actions */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button
                            onClick={saveSettings}
                            disabled={loading}
                            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {loading ? 'Saving...' : 'Save Settings'}
                        </button>
                        <Link
                            href="/"
                            className="px-8 py-4 bg-white/10 border border-white/20 text-white rounded-2xl hover:bg-white/20 font-bold transition-all duration-300 text-center"
                        >
                            Back to Home
                        </Link>
                    </div>

                    {/* Success/Error Message */}
                    {message && (
                        <div className={`text-center p-4 rounded-xl ${
                            message.includes('successfully')
                                ? 'bg-green-500/20 border border-green-400/30 text-green-300'
                                : 'bg-red-500/20 border border-red-400/30 text-red-300'
                        }`}>
                            {message}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SettingsPage;
