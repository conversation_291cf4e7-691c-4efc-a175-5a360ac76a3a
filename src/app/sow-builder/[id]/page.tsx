'use client';

import React, { useEffect, useState } from 'react';
import FormWizard from '../components/form-wizard';

interface PageProps {
    params: Promise<{
        id: string;
    }>;
}

const SOWEditPage = ({ params }: PageProps) => {
    const [sowId, setSowId] = useState<string>('');

    useEffect(() => {
        params.then(resolvedParams => {
            setSowId(resolvedParams.id);
        });
    }, [params]);
    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="container mx-auto px-4">
                <h1 className="text-3xl font-bold text-center mb-8">Edit SOW #{sowId}</h1>
                <FormWizard />
            </div>
        </div>
    );
};

export default SOWEditPage;
