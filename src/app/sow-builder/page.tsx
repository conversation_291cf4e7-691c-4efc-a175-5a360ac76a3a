'use client';

import React from 'react';
import FormWizard from './components/form-wizard';

const SOWBuilderPage = () => {
    return (
        <div className="sow-builder-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
            <div className="container mx-auto px-4 py-12">
                {/* Header */}
                <div className="text-center mb-16">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl mb-6 shadow-2xl">
                        <span className="text-2xl font-bold text-white">📝</span>
                    </div>

                    <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent mb-6">
                        Custom SOW Builder
                    </h1>

                    <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto mb-8">
                        Build comprehensive Statements of Work with our detailed step-by-step wizard
                    </p>

                    <div className="inline-flex items-center bg-blue-500/20 border border-blue-400/30 rounded-full px-6 py-3 text-blue-300 font-medium">
                        <span className="mr-2">🎯</span>
                        Perfect for complex projects requiring detailed specifications
                    </div>
                </div>

                <FormWizard />
            </div>
        </div>
    );
};

export default SOWBuilderPage;