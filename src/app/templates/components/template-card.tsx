import React from 'react';
import Card from '../../components/ui/card';

interface TemplateCardProps {
    title: string;
    description: string;
    onSelect: () => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ title, description, onSelect }) => {
    return (
        <div onClick={onSelect}>
            <Card 
                title={title}
                content={<p className="text-gray-600">{description}</p>}
                className="cursor-pointer hover:shadow-lg transition-shadow"
            />
        </div>
    );
};

export default TemplateCard;