'use client';

import React from 'react';
import TemplateCard from './template-card';
import useTemplates from '../../../hooks/use-templates';

const TemplateGallery = () => {
    const { templates, loading, error } = useTemplates();

    if (loading) {
        return <div>Loading templates...</div>;
    }

    if (error) {
        return <div>Error loading templates: {error}</div>;
    }

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map(template => (
                <TemplateCard
                    key={template.id}
                    title={template.name || (template as any).title || 'Untitled Template'}
                    description={template.description}
                    onSelect={() => console.log('Selected:', template)}
                />
            ))}
        </div>
    );
};

export default TemplateGallery;