'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import LoadingSpinner from '../components/loading-spinner';
import { UserDataService } from '../../lib/services/userDataService';

interface UploadedTemplate {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

const TemplatesPage = () => {
    const [uploadedTemplates, setUploadedTemplates] = useState<UploadedTemplate[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(null);

    // Bulk selection state
    const [selectedTemplates, setSelectedTemplates] = useState<Set<string>>(new Set());
    const [isSelectionMode, setIsSelectionMode] = useState(false);

    useEffect(() => {
        loadUploadedTemplates();
    }, []);

    const loadUploadedTemplates = async () => {
        try {
            const response = await fetch('/api/templates');
            if (response.ok) {
                const templates = await response.json();
                setUploadedTemplates(Array.isArray(templates) ? templates : []);
            }
        } catch (error) {
            console.error('Failed to load templates:', error);
            setUploadedTemplates([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleUseTemplate = (template: UploadedTemplate) => {
        window.location.href = `/sow-generator?templateId=${template.id}`;
    };

    const handleEditTemplate = (template: UploadedTemplate) => {
        window.location.href = `/sow-generator?templateId=${template.id}&edit=true`;
    };

    const handleDeleteTemplate = async (templateId: string) => {
        if (!confirm('Are you sure you want to delete this template?')) {
            return;
        }

        try {
            const response = await fetch(`/api/templates?id=${templateId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                loadUploadedTemplates();
            } else {
                alert('Failed to delete template');
            }
        } catch (error) {
            console.error('Failed to delete template:', error);
            alert('Failed to delete template');
        }
    };

    const handlePreviewTemplate = (templateId: string) => {
        setPreviewTemplateId(templateId);
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Bulk selection functions
    const toggleSelectionMode = () => {
        setIsSelectionMode(!isSelectionMode);
        setSelectedTemplates(new Set());
    };

    const toggleTemplateSelection = (templateId: string) => {
        const newSelected = new Set(selectedTemplates);
        if (newSelected.has(templateId)) {
            newSelected.delete(templateId);
        } else {
            newSelected.add(templateId);
        }
        setSelectedTemplates(newSelected);
    };

    const selectAllTemplates = () => {
        if (selectedTemplates.size === uploadedTemplates.length) {
            setSelectedTemplates(new Set());
        } else {
            setSelectedTemplates(new Set(uploadedTemplates.map(template => template.id)));
        }
    };

    const handleBulkDelete = async () => {
        if (selectedTemplates.size === 0) {
            alert('Please select templates to delete');
            return;
        }

        const count = selectedTemplates.size;
        if (!confirm(`Are you sure you want to delete ${count} template${count > 1 ? 's' : ''}? This action cannot be undone.`)) {
            return;
        }

        try {
            const deletePromises = Array.from(selectedTemplates).map(templateId =>
                fetch(`/api/templates?id=${templateId}`, { method: 'DELETE' })
            );

            const results = await Promise.allSettled(deletePromises);

            // Count failures properly - check both rejected promises and failed HTTP responses
            const failures = results.filter(result =>
                result.status === 'rejected' ||
                (result.status === 'fulfilled' && !result.value.ok)
            ).length;

            if (failures > 0) {
                alert(`${count - failures} templates deleted successfully. ${failures} failed to delete.`);
            } else {
                alert(`${count} template${count > 1 ? 's' : ''} deleted successfully!`);
            }

            // Reset selection and reload
            setSelectedTemplates(new Set());
            setIsSelectionMode(false);
            loadUploadedTemplates();
        } catch (error) {
            console.error('Bulk delete error:', error);
            alert('Failed to delete templates');
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <LoadingSpinner size="lg" color="white" text="Loading templates..." />
            </div>
        );
    }

    return (
        <div className="min-h-screen relative overflow-hidden">

            <div className="relative z-10 container mx-auto px-6 py-24">


                {/* Uploaded Templates Section */}
                {uploadedTemplates.length > 0 ? (
                    <div className="mb-16">
                        <div className="text-center mb-8">
                            <div className="flex flex-col md:flex-row justify-between items-center max-w-7xl mx-auto">
                                <div className="text-center md:text-left">
                                    <h2 className="text-3xl font-bold text-white mb-2">Your Uploaded Templates</h2>
                                    <p className="text-slate-300">
                                        {isSelectionMode
                                            ? `${selectedTemplates.size} of ${uploadedTemplates.length} templates selected`
                                            : 'DOCX templates you\'ve uploaded for SOW generation'
                                        }
                                    </p>
                                </div>
                                <div className="flex space-x-4 mt-4 md:mt-0">
                                    {isSelectionMode ? (
                                        <>
                                            <button
                                                onClick={selectAllTemplates}
                                                className="px-4 py-2 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-xl hover:bg-blue-500/30 font-bold transition-all duration-300"
                                            >
                                                {selectedTemplates.size === uploadedTemplates.length ? (
                                                    <>
                                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                        Deselect All
                                                    </>
                                                ) : (
                                                    <>
                                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                        </svg>
                                                        Select All
                                                    </>
                                                )}
                                            </button>
                                            <button
                                                onClick={handleBulkDelete}
                                                disabled={selectedTemplates.size === 0}
                                                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                Delete ({selectedTemplates.size})
                                            </button>
                                            <button
                                                onClick={toggleSelectionMode}
                                                className="px-4 py-2 bg-gray-500/20 border border-gray-400/30 text-gray-300 rounded-xl hover:bg-gray-500/30 font-bold transition-all duration-300"
                                            >
                                                Cancel
                                            </button>
                                        </>
                                    ) : (
                                        <button
                                            onClick={toggleSelectionMode}
                                            className="px-4 py-2 bg-orange-500/20 border border-orange-400/30 text-orange-300 rounded-xl hover:bg-orange-500/30 font-bold transition-all duration-300"
                                        >
                                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                            </svg>
                                            Select Multiple
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto mb-12">
                            {uploadedTemplates.map(template => (
                                <div key={template.id} className={`group relative bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-xl border border-white/20 p-8 rounded-3xl hover:from-white/15 hover:via-white/10 hover:to-white/5 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/10 ${
                                    isSelectionMode && selectedTemplates.has(template.id) ? 'ring-2 ring-blue-400 bg-blue-500/10' : ''
                                }`}>
                                    {/* Gradient overlay */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                    {/* Content */}
                                    <div className="relative z-10">
                                        <div className="flex items-start justify-between mb-4">
                                            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-xl shadow-purple-500/25">
                                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            </div>
                                            {isSelectionMode && (
                                                <input
                                                    type="checkbox"
                                                    checked={selectedTemplates.has(template.id)}
                                                    onChange={() => toggleTemplateSelection(template.id)}
                                                    className="w-5 h-5 bg-white/10 border border-white/30 rounded text-blue-600 focus:ring-blue-500 focus:ring-2"
                                                />
                                            )}
                                        </div>

                                        <h3 className="text-lg font-bold text-white mb-2 break-words overflow-hidden">{template.name}</h3>
                                        <p className="text-slate-300 mb-4 text-sm">
                                            Uploaded: {new Date(template.uploadDate).toLocaleDateString()}
                                            <br />
                                            Size: {formatFileSize(template.fileSize)}
                                            <br />
                                            Fields: {template.fields.length} detected
                                        </p>

                                        {/* Saved Data Status */}
                                        {UserDataService.hasUserData(template.id) && (
                                            <div className="mb-4 p-3 bg-green-500/10 border border-green-400/30 rounded-xl">
                                                <div className="flex items-center space-x-2">
                                                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                                    <span className="text-green-300 text-xs font-medium">
                                                        {UserDataService.getUserDataSummary(template.id)}
                                                    </span>
                                                </div>
                                            </div>
                                        )}

                                        {!isSelectionMode && (
                                            <div className="space-y-2">
                                                <div className="flex gap-2">
                                                    <button
                                                        onClick={() => handleUseTemplate(template)}
                                                        className="flex-1 py-2 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 text-sm"
                                                    >
                                                        Use Template
                                                    </button>
                                                    <button
                                                        onClick={() => handlePreviewTemplate(template.id)}
                                                        className="py-2 px-3 bg-green-500/20 border border-green-400/30 text-green-300 rounded-xl hover:bg-green-500/30 transition-all duration-300 text-sm"
                                                        title="Preview DOCX"
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                    </button>
                                                    <button
                                                        onClick={() => handleDeleteTemplate(template.id)}
                                                        className="py-2 px-3 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 transition-all duration-300 text-sm"
                                                        title="Delete Template"
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                                </div>

                                                {/* Edit Button - Show if has saved data */}
                                                {UserDataService.hasUserData(template.id) && (
                                                    <button
                                                        onClick={() => handleEditTemplate(template)}
                                                        className="w-full py-2 px-4 bg-orange-500/20 border border-orange-400/30 text-orange-300 rounded-xl hover:bg-orange-500/30 font-bold transition-all duration-300 text-sm flex items-center justify-center space-x-2"
                                                        title="Edit saved information"
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                        </svg>
                                                        <span>Edit Saved Data</span>
                                                    </button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    <div className="mb-16">
                        <div className="text-center">
                            <div className="relative bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-xl border border-white/20 rounded-3xl p-16 max-w-3xl mx-auto overflow-hidden">
                                <div className="relative z-10">
                                    <div className="w-24 h-24 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-purple-500/25">
                                        <svg className="w-10 h-10 text-white animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                    </div>

                                    <h2 className="text-4xl font-black bg-gradient-to-r from-white via-blue-200 to-purple-300 bg-clip-text text-transparent mb-6">
                                        Ready to Create Magic?
                                    </h2>
                                    <p className="text-xl text-slate-300 mb-10 max-w-lg mx-auto leading-relaxed">
                                        Upload your first DOCX template and watch our AI transform it into
                                        <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-semibold"> intelligent, customizable SOWs</span>
                                    </p>

                                    <Link
                                        href="/sow-generator"
                                        className="group inline-flex items-center bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 px-10 py-5 rounded-2xl text-white font-bold hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 transition-all duration-500 transform hover:scale-105 shadow-2xl hover:shadow-purple-500/30"
                                    >
                                        <svg className="mr-4 w-6 h-6 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        <span className="text-lg">Upload Template & Generate SOW</span>
                                        <svg className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* DOCX Preview Modal */}
            {previewTemplateId && (
                <DocxPreviewModal 
                    templateId={previewTemplateId} 
                    onClose={() => setPreviewTemplateId(null)} 
                />
            )}
        </div>
    );
};

// DOCX Preview Modal Component
const DocxPreviewModal = ({ templateId, onClose }: { templateId: string; onClose: () => void }) => {
    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 className="text-xl font-bold text-gray-900">Template Preview</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                    >
                        ×
                    </button>
                </div>
                <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
                    <DocxViewer templateId={templateId} />
                </div>
            </div>
        </div>
    );
};

// DOCX Viewer Component using docx-preview
const DocxViewer = ({ templateId }: { templateId: string }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const loadDocxPreview = async () => {
            try {
                setIsLoading(true);
                setError(null);

                // Import docx-preview dynamically (client-side only)
                const { renderAsync } = await import('docx-preview');

                // Fetch the DOCX file directly
                const response = await fetch(`/api/template/${templateId}/download`);
                if (!response.ok) {
                    throw new Error('Failed to load template');
                }

                const arrayBuffer = await response.arrayBuffer();
                const container = document.getElementById(`docx-container-${templateId}`);

                if (container) {
                    await renderAsync(arrayBuffer, container, undefined, {
                        className: 'docx-wrapper',
                        inWrapper: true,
                        ignoreWidth: false,
                        ignoreHeight: false,
                        ignoreFonts: false,
                        breakPages: true,
                        ignoreLastRenderedPageBreak: true,
                        experimental: false,
                        trimXmlDeclaration: true,
                        useBase64URL: false
                    });
                }
            } catch (err) {
                console.error('Failed to load DOCX preview:', err);
                setError(err instanceof Error ? err.message : 'Failed to load preview');
            } finally {
                setIsLoading(false);
            }
        };

        loadDocxPreview();
    }, [templateId]);



    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-96">
                <LoadingSpinner size="lg" color="blue" text="Loading DOCX preview..." />
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center h-96 text-red-600">
                <div className="text-center">
                    <p className="text-lg font-semibold mb-2">Failed to load preview</p>
                    <p className="text-sm">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div 
            id={`docx-container-${templateId}`} 
            className="docx-preview-container"
            style={{ minHeight: '500px' }}
        />
    );
};

export default TemplatesPage;
