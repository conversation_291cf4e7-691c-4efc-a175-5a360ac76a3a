import { useState } from 'react';
import { SOW } from '../lib/types/sow';

const useSOW = () => {
    const [sowData, setSowData] = useState<SOW | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const fetchSOW = async (id: string) => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await fetch(`/api/sow/${id}`);
            if (!response.ok) {
                throw new Error('Failed to fetch SOW');
            }
            const data: SOW = await response.json();
            setSowData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const updateSOW = async (updatedSOW: SOW) => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await fetch(`/api/sow/${updatedSOW.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedSOW),
            });
            if (!response.ok) {
                throw new Error('Failed to update SOW');
            }
            const data: SOW = await response.json();
            setSowData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    return {
        sowData,
        isLoading,
        error,
        fetchSOW,
        updateSOW,
    };
};

export default useSOW;