interface AutofillInput {
  companyName?: string;
  projectType?: string;
  budget?: string;
  timeline?: string;
  clientName?: string;
  clientEmail?: string;
  projectDescription?: string;
}

export interface AutofillOutput {
  companyInfo: {
    companyName: string;
    contactName: string;
    email: string;
    phone: string;
    address: string;
    website: string;
  };
  clientInfo: {
    clientName: string;
    clientCompany: string;
    clientEmail: string;
    clientPhone: string;
    clientAddress: string;
  };
  projectDetails: {
    projectName: string;
    projectType: string;
    projectComplexity: string;
    projectDescription: string;
    objectives: string[];
    deliverables: string[];
  };
  scopeOfWork: {
    phases: Array<{
      name: string;
      description: string;
      deliverables: string[];
      timeline: string;
    }>;
    inclusions: string[];
    exclusions: string[];
  };
  timeline: {
    startDate: string;
    endDate: string;
    milestones: Array<{
      name: string;
      date: string;
      description: string;
    }>;
  };
  pricing: {
    totalBudget: number;
    breakdown: Array<{
      item: string;
      cost: number;
      description: string;
    }>;
    paymentTerms: string;
    paymentSchedule: Array<{
      milestone: string;
      percentage: number;
      amount: number;
    }>;
  };
  terms: {
    warrantyPeriod: string;
    supportPeriod: string;
    revisionRounds: number;
    cancellationPolicy: string;
    intellectualProperty: string;
  };
}

// QuantumRhino company defaults
const QUANTUMRHINO_DEFAULTS = {
  companyName: "QuantumRhino",
  contactName: "Chase Vazquez",
  email: "<EMAIL>",
  phone: "+****************",
  address: "123 Innovation Drive, Tech City, TC 12345",
  website: "https://quantumrhino.com",
};

// Project type templates
const PROJECT_TEMPLATES = {
  "web-application": {
    phases: [
      {
        name: "Discovery & Planning",
        description: "Requirements gathering, technical architecture, and project planning",
        deliverables: ["Technical Requirements Document", "Project Timeline", "Wireframes"],
        timeline: "2 weeks"
      },
      {
        name: "Design & Prototyping",
        description: "UI/UX design, user experience optimization, and interactive prototypes",
        deliverables: ["Design System", "High-fidelity Mockups", "Interactive Prototype"],
        timeline: "3 weeks"
      },
      {
        name: "Development",
        description: "Frontend and backend development, database setup, and API integration",
        deliverables: ["Functional Web Application", "Database Schema", "API Documentation"],
        timeline: "8 weeks"
      },
      {
        name: "Testing & Deployment",
        description: "Quality assurance, performance testing, and production deployment",
        deliverables: ["Test Reports", "Deployed Application", "User Documentation"],
        timeline: "2 weeks"
      }
    ],
    inclusions: [
      "Responsive web design",
      "Cross-browser compatibility",
      "Basic SEO optimization",
      "SSL certificate setup",
      "Basic analytics integration"
    ],
    exclusions: [
      "Third-party integrations not specified",
      "Content creation and copywriting",
      "Ongoing maintenance beyond warranty period",
      "Advanced SEO services",
      "Social media integration"
    ]
  },
  "mobile-application": {
    phases: [
      {
        name: "Discovery & Planning",
        description: "Requirements gathering, platform selection, and technical architecture",
        deliverables: ["Technical Requirements Document", "Platform Strategy", "Wireframes"],
        timeline: "2 weeks"
      },
      {
        name: "Design & Prototyping",
        description: "Mobile UI/UX design and interactive prototypes",
        deliverables: ["Design System", "Mobile Mockups", "Interactive Prototype"],
        timeline: "3 weeks"
      },
      {
        name: "Development",
        description: "Native or cross-platform development, backend API, and testing",
        deliverables: ["Mobile Application", "Backend API", "App Store Assets"],
        timeline: "10 weeks"
      },
      {
        name: "Testing & Deployment",
        description: "Device testing, app store submission, and launch support",
        deliverables: ["Test Reports", "App Store Listing", "Launch Documentation"],
        timeline: "2 weeks"
      }
    ],
    inclusions: [
      "iOS and Android compatibility",
      "App store submission",
      "Push notification setup",
      "Basic analytics integration",
      "Offline functionality"
    ],
    exclusions: [
      "App store optimization services",
      "Marketing and promotion",
      "Third-party SDK integrations not specified",
      "Ongoing app store management",
      "Advanced analytics and reporting"
    ]
  },
  "e-commerce": {
    phases: [
      {
        name: "Discovery & Planning",
        description: "Business requirements, payment gateway selection, and architecture planning",
        deliverables: ["Business Requirements Document", "Technical Architecture", "Payment Strategy"],
        timeline: "2 weeks"
      },
      {
        name: "Design & User Experience",
        description: "E-commerce UI/UX design focused on conversion optimization",
        deliverables: ["Design System", "Product Page Designs", "Checkout Flow"],
        timeline: "4 weeks"
      },
      {
        name: "Development",
        description: "E-commerce platform development, payment integration, and inventory management",
        deliverables: ["E-commerce Platform", "Payment Integration", "Admin Dashboard"],
        timeline: "12 weeks"
      },
      {
        name: "Testing & Launch",
        description: "Payment testing, security audit, and production deployment",
        deliverables: ["Security Audit Report", "Live E-commerce Site", "Admin Training"],
        timeline: "3 weeks"
      }
    ],
    inclusions: [
      "Payment gateway integration",
      "Inventory management system",
      "Order management",
      "Basic SEO optimization",
      "SSL certificate and security"
    ],
    exclusions: [
      "Product photography",
      "Content creation and product descriptions",
      "Marketing automation setup",
      "Advanced analytics and reporting",
      "Third-party marketplace integrations"
    ]
  }
};

export class AutofillService {
  static generateSOW(input: AutofillInput): AutofillOutput {
    const projectTemplate = PROJECT_TEMPLATES[input.projectType as keyof typeof PROJECT_TEMPLATES] || PROJECT_TEMPLATES["web-application"];
    
    // Parse budget
    const budgetNumber = this.parseBudget(input.budget || "50000");
    
    // Generate timeline dates
    const timelineWeeks = this.parseTimeline(input.timeline || "15 weeks");
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + timelineWeeks * 7 * 24 * 60 * 60 * 1000);
    
    return {
      companyInfo: {
        ...QUANTUMRHINO_DEFAULTS,
      },
      clientInfo: {
        clientName: input.clientName || "Client Name",
        clientCompany: input.companyName || "Client Company",
        clientEmail: input.clientEmail || "<EMAIL>",
        clientPhone: "+****************",
        clientAddress: "Client Address, City, State 12345",
      },
      projectDetails: {
        projectName: `${input.companyName || "Client"} ${this.capitalizeProjectType(input.projectType || "web-application")}`,
        projectType: input.projectType || "web-application",
        projectComplexity: this.determineComplexity(budgetNumber),
        projectDescription: input.projectDescription || this.generateProjectDescription(input.projectType || "web-application"),
        objectives: this.generateObjectives(input.projectType || "web-application"),
        deliverables: this.generateDeliverables(input.projectType || "web-application"),
      },
      scopeOfWork: {
        phases: projectTemplate.phases,
        inclusions: projectTemplate.inclusions,
        exclusions: projectTemplate.exclusions,
      },
      timeline: {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        milestones: this.generateMilestones(projectTemplate.phases, startDate),
      },
      pricing: {
        totalBudget: budgetNumber,
        breakdown: this.generatePricingBreakdown(budgetNumber, input.projectType || "web-application"),
        paymentTerms: "Net 30 days",
        paymentSchedule: this.generatePaymentSchedule(budgetNumber, projectTemplate.phases.length),
      },
      terms: {
        warrantyPeriod: "90 days",
        supportPeriod: "30 days",
        revisionRounds: 3,
        cancellationPolicy: "30 days written notice",
        intellectualProperty: "Client owns all deliverables upon final payment",
      },
    };
  }

  private static parseBudget(budget: string): number {
    const cleanBudget = budget.replace(/[^0-9]/g, '');
    return parseInt(cleanBudget) || 50000;
  }

  private static parseTimeline(timeline: string): number {
    const weeks = timeline.match(/(\d+)\s*weeks?/i);
    if (weeks) return parseInt(weeks[1]);
    
    const months = timeline.match(/(\d+)\s*months?/i);
    if (months) return parseInt(months[1]) * 4;
    
    return 15; // Default 15 weeks
  }

  private static capitalizeProjectType(type: string): string {
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  private static determineComplexity(budget: number): string {
    if (budget < 25000) return "Simple";
    if (budget < 75000) return "Medium";
    return "Complex";
  }

  private static generateProjectDescription(projectType: string): string {
    const descriptions = {
      "web-application": "A modern, responsive web application designed to meet your business needs with intuitive user experience and robust functionality.",
      "mobile-application": "A native mobile application that provides seamless user experience across iOS and Android platforms with offline capabilities.",
      "e-commerce": "A comprehensive e-commerce platform with secure payment processing, inventory management, and optimized conversion flows."
    };
    
    return descriptions[projectType as keyof typeof descriptions] || descriptions["web-application"];
  }

  private static generateObjectives(projectType: string): string[] {
    const objectives = {
      "web-application": [
        "Create a user-friendly and intuitive interface",
        "Ensure responsive design across all devices",
        "Implement secure user authentication and data protection",
        "Optimize for search engines and performance",
        "Provide scalable architecture for future growth"
      ],
      "mobile-application": [
        "Deliver native mobile experience on iOS and Android",
        "Implement offline functionality for core features",
        "Ensure smooth performance and fast loading times",
        "Integrate push notifications for user engagement",
        "Provide intuitive navigation and user interface"
      ],
      "e-commerce": [
        "Create seamless shopping experience",
        "Implement secure payment processing",
        "Optimize for conversion and sales",
        "Provide comprehensive admin dashboard",
        "Ensure mobile-responsive design"
      ]
    };
    
    return objectives[projectType as keyof typeof objectives] || objectives["web-application"];
  }

  private static generateDeliverables(projectType: string): string[] {
    const deliverables = {
      "web-application": [
        "Fully functional web application",
        "Responsive design for all devices",
        "User authentication system",
        "Admin dashboard",
        "Documentation and training materials"
      ],
      "mobile-application": [
        "Native mobile applications (iOS & Android)",
        "App store submission and approval",
        "Push notification system",
        "Offline functionality",
        "User documentation"
      ],
      "e-commerce": [
        "Complete e-commerce platform",
        "Payment gateway integration",
        "Inventory management system",
        "Order processing workflow",
        "Admin training and documentation"
      ]
    };
    
    return deliverables[projectType as keyof typeof deliverables] || deliverables["web-application"];
  }

  private static generateMilestones(phases: any[], startDate: Date): Array<{name: string; date: string; description: string}> {
    const milestones: Array<{name: string; date: string; description: string}> = [];
    let currentDate = new Date(startDate);
    
    phases.forEach((phase, index) => {
      const weeks = parseInt(phase.timeline.match(/(\d+)/)?.[1] || "2");
      currentDate = new Date(currentDate.getTime() + weeks * 7 * 24 * 60 * 60 * 1000);
      
      milestones.push({
        name: `${phase.name} Complete`,
        date: currentDate.toISOString().split('T')[0],
        description: `Completion of ${phase.name} phase with all deliverables`
      });
    });
    
    return milestones;
  }

  private static generatePricingBreakdown(totalBudget: number, projectType: string): Array<{item: string; cost: number; description: string}> {
    const breakdowns = {
      "web-application": [
        { item: "Discovery & Planning", percentage: 0.15, description: "Requirements gathering and project planning" },
        { item: "Design & Prototyping", percentage: 0.25, description: "UI/UX design and interactive prototypes" },
        { item: "Development", percentage: 0.50, description: "Frontend and backend development" },
        { item: "Testing & Deployment", percentage: 0.10, description: "Quality assurance and deployment" }
      ],
      "mobile-application": [
        { item: "Discovery & Planning", percentage: 0.15, description: "Requirements and platform strategy" },
        { item: "Design & Prototyping", percentage: 0.20, description: "Mobile UI/UX design" },
        { item: "Development", percentage: 0.55, description: "Native app development" },
        { item: "Testing & Deployment", percentage: 0.10, description: "Testing and app store submission" }
      ],
      "e-commerce": [
        { item: "Discovery & Planning", percentage: 0.12, description: "Business requirements and architecture" },
        { item: "Design & UX", percentage: 0.20, description: "E-commerce design and user experience" },
        { item: "Development", percentage: 0.58, description: "Platform development and integrations" },
        { item: "Testing & Launch", percentage: 0.10, description: "Security testing and launch" }
      ]
    };
    
    const breakdown = breakdowns[projectType as keyof typeof breakdowns] || breakdowns["web-application"];
    
    return breakdown.map(item => ({
      item: item.item,
      cost: Math.round(totalBudget * item.percentage),
      description: item.description
    }));
  }

  private static generatePaymentSchedule(totalBudget: number, phaseCount: number): Array<{milestone: string; percentage: number; amount: number}> {
    const schedules = [
      { milestone: "Project Kickoff", percentage: 0.30, amount: Math.round(totalBudget * 0.30) },
      { milestone: "Design Approval", percentage: 0.25, amount: Math.round(totalBudget * 0.25) },
      { milestone: "Development Milestone", percentage: 0.25, amount: Math.round(totalBudget * 0.25) },
      { milestone: "Project Completion", percentage: 0.20, amount: Math.round(totalBudget * 0.20) }
    ];
    
    return schedules;
  }
}
