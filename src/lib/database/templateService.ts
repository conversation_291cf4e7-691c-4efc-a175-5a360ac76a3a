export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  content: any;
  createdAt: Date;
  updatedAt: Date;
}

// Mock template service - replace with actual database operations
export class TemplateService {
  private static templates: Template[] = [
    {
      id: '1',
      name: 'Default SOW Template',
      description: 'A basic statement of work template',
      category: 'general',
      content: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: 'Consulting SOW Template',
      description: 'Template for consulting projects',
      category: 'consulting',
      content: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      name: 'Development SOW Template',
      description: 'Template for software development projects',
      category: 'development',
      content: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  static async getAllTemplates(): Promise<Template[]> {
    return this.templates;
  }

  static async getTemplateById(id: string): Promise<Template | null> {
    return this.templates.find(template => template.id === id) || null;
  }

  static async createTemplate(templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> {
    const newTemplate: Template = {
      ...templateData,
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.templates.push(newTemplate);
    return newTemplate;
  }

  static async updateTemplate(id: string, updates: Partial<Omit<Template, 'id' | 'createdAt'>>): Promise<Template | null> {
    const templateIndex = this.templates.findIndex(template => template.id === id);
    if (templateIndex === -1) return null;

    this.templates[templateIndex] = {
      ...this.templates[templateIndex],
      ...updates,
      updatedAt: new Date(),
    };
    return this.templates[templateIndex];
  }

  static async deleteTemplate(id: string): Promise<boolean> {
    const templateIndex = this.templates.findIndex(template => template.id === id);
    if (templateIndex === -1) return false;

    this.templates.splice(templateIndex, 1);
    return true;
  }
}

// Export convenience functions
export const getTemplates = () => TemplateService.getAllTemplates();
export const createTemplate = (templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>) => TemplateService.createTemplate(templateData);
export const updateTemplate = (id: string, updates: Partial<Omit<Template, 'id' | 'createdAt'>>) => TemplateService.updateTemplate(id, updates);
export const deleteTemplate = (id: string) => TemplateService.deleteTemplate(id);
