import { AutofillOutput } from '../autofill/autofill-service';

export class MarkdownGenerator {
  static generateSOWMarkdown(data: AutofillOutput): string {
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `---
title: "Statement of Work"
subtitle: "${data.projectDetails.projectName}"
author: "${data.companyInfo.companyName}"
date: "${currentDate}"
geometry: margin=1in
fontsize: 11pt
linestretch: 1.15
documentclass: article
header-includes:
  - \\usepackage{fancyhdr}
  - \\pagestyle{fancy}
  - \\fancyhead[L]{${data.companyInfo.companyName}}
  - \\fancyhead[R]{Statement of Work}
  - \\fancyfoot[C]{\\thepage}
---

# Statement of Work

**${data.projectDetails.projectName}**

---

## Project Overview

**Client:** ${data.clientInfo.clientCompany}  
**Project Type:** ${data.projectDetails.projectType.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}  
**Project Timeline:** ${data.timeline.startDate} to ${data.timeline.endDate}  
**Total Investment:** $${data.pricing.totalBudget.toLocaleString()}  
**Document Date:** ${currentDate}

---

## Company Information

**Service Provider:**  
${data.companyInfo.companyName}  
${data.companyInfo.address}  
Phone: ${data.companyInfo.phone}  
Email: ${data.companyInfo.email}  
Website: ${data.companyInfo.website}

**Primary Contact:**  
${data.companyInfo.contactName}  
${data.companyInfo.email}

---

## Client Information

**Client Company:**  
${data.clientInfo.clientCompany}  
${data.clientInfo.clientAddress}

**Primary Contact:**  
${data.clientInfo.clientName}  
Phone: ${data.clientInfo.clientPhone}  
Email: ${data.clientInfo.clientEmail}

---

## Project Description

${data.projectDetails.projectDescription}

### Project Objectives

${data.projectDetails.objectives.map(obj => `- ${obj}`).join('\\n')}

### Key Deliverables

${data.projectDetails.deliverables.map(deliverable => `- ${deliverable}`).join('\\n')}

---

## Scope of Work

### Project Phases

${data.scopeOfWork.phases.map((phase, index) => `
#### Phase ${index + 1}: ${phase.name}

**Duration:** ${phase.timeline}

**Description:** ${phase.description}

**Deliverables:**
${phase.deliverables.map(deliverable => `- ${deliverable}`).join('\\n')}
`).join('\\n')}

### What's Included

${data.scopeOfWork.inclusions.map(inclusion => `- ${inclusion}`).join('\\n')}

### What's Not Included

${data.scopeOfWork.exclusions.map(exclusion => `- ${exclusion}`).join('\\n')}

---

## Timeline & Milestones

**Project Start Date:** ${data.timeline.startDate}  
**Project End Date:** ${data.timeline.endDate}

### Key Milestones

${data.timeline.milestones.map(milestone => `
**${milestone.name}**  
Date: ${milestone.date}  
Description: ${milestone.description}
`).join('\\n')}

---

## Investment & Payment Terms

### Total Project Investment

**Total Cost:** $${data.pricing.totalBudget.toLocaleString()}

### Cost Breakdown

${data.pricing.breakdown.map(item => `
**${item.item}:** $${item.cost.toLocaleString()}  
${item.description}
`).join('\\n')}

### Payment Schedule

${data.pricing.paymentSchedule.map(payment => `
**${payment.milestone}**  
Amount: $${payment.amount.toLocaleString()} (${payment.percentage * 100}% of total)
`).join('\\n')}

### Payment Terms

- **Payment Terms:** ${data.pricing.paymentTerms}
- **Accepted Payment Methods:** Bank transfer, check, or ACH
- **Late Payment:** 1.5% monthly service charge on overdue amounts
- **Currency:** All amounts in USD

---

## Terms and Conditions

### Project Management

- **Communication:** Regular updates via email and scheduled calls
- **Project Manager:** Assigned dedicated project manager
- **Reporting:** Weekly progress reports and milestone updates
- **Change Requests:** Any scope changes require written approval and may affect timeline and cost

### Quality Assurance

- **Testing:** Comprehensive testing across all specified platforms and browsers
- **Revisions:** Up to ${data.terms.revisionRounds} rounds of revisions included per deliverable
- **Quality Standards:** All work follows industry best practices and coding standards

### Support & Warranty

- **Warranty Period:** ${data.terms.warrantyPeriod} from project completion
- **Support Period:** ${data.terms.supportPeriod} of complimentary support included
- **Bug Fixes:** Free bug fixes during warranty period
- **Training:** Basic training session included for admin users

### Intellectual Property

- **Ownership:** ${data.terms.intellectualProperty}
- **Source Code:** Client receives all source code upon final payment
- **Third-party Components:** Any third-party components remain under their respective licenses
- **Confidentiality:** Both parties agree to maintain confidentiality of proprietary information

### Project Cancellation

- **Cancellation Policy:** ${data.terms.cancellationPolicy}
- **Work Completed:** Client pays for all work completed up to cancellation date
- **Deliverables:** Client receives all completed deliverables upon payment

### Limitation of Liability

- **Liability Cap:** Total liability limited to the total project cost
- **Consequential Damages:** Neither party liable for indirect or consequential damages
- **Force Majeure:** Neither party responsible for delays due to circumstances beyond reasonable control

---

## Acceptance Criteria

### Deliverable Acceptance

- **Review Period:** Client has 5 business days to review each deliverable
- **Acceptance:** Deliverables deemed accepted if no feedback provided within review period
- **Rejection:** Must include specific, actionable feedback for any rejected deliverables

### Project Completion

- **Final Acceptance:** Project considered complete upon client acceptance of all deliverables
- **Final Payment:** Due within 30 days of project completion
- **Handover:** Complete project handover including documentation and training

---

## Signatures

**Client Approval:**

Name: _________________________________  
Title: _________________________________  
Date: _________________________________  
Signature: _____________________________

**${data.companyInfo.companyName} Approval:**

Name: ${data.companyInfo.contactName}  
Title: Project Manager  
Date: ${currentDate}  
Signature: _____________________________

---

*This Statement of Work constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements relating to the subject matter herein. This agreement may only be modified in writing signed by both parties.*

**Document Version:** 1.0  
**Last Updated:** ${currentDate}  
**Prepared by:** ${data.companyInfo.companyName}`;
  }

  static generateQuickSOW(companyName: string, projectType: string, budget: string): string {
    // For ultra-quick generation with minimal input
    const data = {
      companyInfo: {
        companyName: "QuantumRhino",
        contactName: "Chase Vazquez",
        email: "<EMAIL>",
        phone: "+****************",
        address: "123 Innovation Drive, Tech City, TC 12345",
        website: "https://quantumrhino.com"
      },
      clientInfo: {
        clientCompany: companyName,
        clientName: "Client Representative",
        clientEmail: "<EMAIL>",
        clientPhone: "+****************",
        clientAddress: "Client Address, City, State 12345"
      },
      projectDetails: {
        projectName: `${companyName} ${projectType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`,
        projectType: projectType,
        projectComplexity: "Medium",
        projectDescription: `A professional ${projectType.replace('-', ' ')} solution tailored for ${companyName}.`,
        objectives: ["Deliver high-quality solution", "Meet project timeline", "Ensure client satisfaction"],
        deliverables: ["Completed project", "Documentation", "Training"]
      },
      scopeOfWork: {
        phases: [
          {
            name: "Planning & Design",
            timeline: "2-3 weeks",
            description: "Project planning and design phase",
            deliverables: ["Project plan", "Design mockups"]
          },
          {
            name: "Development",
            timeline: "6-8 weeks", 
            description: "Core development phase",
            deliverables: ["Functional application", "Testing"]
          },
          {
            name: "Deployment",
            timeline: "1-2 weeks",
            description: "Final deployment and handover",
            deliverables: ["Live application", "Documentation"]
          }
        ],
        inclusions: ["Core functionality", "Basic testing", "Documentation"],
        exclusions: ["Third-party integrations", "Ongoing maintenance", "Content creation"]
      },
      timeline: {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        milestones: [
          {
            name: "Design Approval",
            date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            description: "Design phase completion"
          },
          {
            name: "Development Complete",
            date: new Date(Date.now() + 70 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            description: "Development phase completion"
          }
        ]
      },
      pricing: {
        totalBudget: parseInt(budget.replace(/[^0-9]/g, '')) || 50000,
        breakdown: [
          { item: "Planning & Design", cost: Math.round((parseInt(budget.replace(/[^0-9]/g, '')) || 50000) * 0.3), description: "Initial planning and design" },
          { item: "Development", cost: Math.round((parseInt(budget.replace(/[^0-9]/g, '')) || 50000) * 0.6), description: "Core development work" },
          { item: "Deployment", cost: Math.round((parseInt(budget.replace(/[^0-9]/g, '')) || 50000) * 0.1), description: "Final deployment" }
        ],
        paymentTerms: "Net 30 days",
        paymentSchedule: [
          { milestone: "Project Start", percentage: 0.5, amount: Math.round((parseInt(budget.replace(/[^0-9]/g, '')) || 50000) * 0.5) },
          { milestone: "Project Completion", percentage: 0.5, amount: Math.round((parseInt(budget.replace(/[^0-9]/g, '')) || 50000) * 0.5) }
        ]
      },
      terms: {
        warrantyPeriod: "90 days",
        supportPeriod: "30 days", 
        revisionRounds: 3,
        cancellationPolicy: "30 days notice",
        intellectualProperty: "Client owns upon final payment"
      }
    };

    return this.generateSOWMarkdown(data);
  }
}
