export interface SOW {
  id: number;
  projectName: string;
  clientName: string;
  vendorName: string;
  scopeOfWork: string;
  timeline: string;
  pricing: string;
  createdAt: Date;
  updatedAt: Date;
  companyId: number;
  company?: Company;
  services?: Service[];
}

export interface Company {
  id: number;
  name: string;
  address?: string;
  email?: string;
  phone?: string;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
}

export interface SOWFormData {
  companyInfo: {
    name: string;
    address?: string;
    email?: string;
    phone?: string;
  };
  projectDetails: {
    projectName: string;
    projectType: string;
    projectComplexity: string;
    description: string;
  };
  scopeOfWork: {
    description: string;
    deliverables: string;
    resources: string;
  };
  timeline: {
    milestones: Milestone[];
  };
  pricing: {
    totalCost: number;
    paymentTerms: string;
  };
}

export interface Milestone {
  title: string;
  date: string;
}
