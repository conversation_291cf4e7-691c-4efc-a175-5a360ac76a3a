export interface Template {
    id: string;
    name: string;
    title?: string;
    description: string;
    content?: any;
    createdAt?: Date;
    updatedAt?: Date;
    // Enhanced fields for user data persistence
    savedUserData?: SavedUserData;
    lastUsed?: Date;
    usageCount?: number;
    autosaveEnabled?: boolean;
}

export interface SavedUserData {
    // Client Information
    clientName?: string;
    clientEmail?: string;
    clientCompany?: string;
    clientPhone?: string;
    clientAddress?: string;
    clientTitle?: string;
    clientDepartment?: string;

    // Project Information
    projectName?: string;
    projectType?: string;
    projectDescription?: string;

    // Timeline
    startDate?: string;
    endDate?: string;
    duration?: string;

    // Pricing
    hourlyRate?: string;
    estimatedHours?: string;
    totalBudget?: string;

    // Payment Terms
    paymentTerms?: string;
    paymentSchedule?: PaymentScheduleItem[];

    // Company/Vendor Information
    companyName?: string;
    companyAddress?: string;
    companyEmail?: string;
    companyPhone?: string;
    contactName?: string;
    contactTitle?: string;

    // Additional custom fields
    customFields?: Record<string, any>;

    // Metadata
    lastSaved?: Date;
    version?: number;
}

export interface PaymentScheduleItem {
    id: string;
    description: string;
    amount: string;
    dueDate: string;
    percentage?: number;
}
